@charset "utf-8";
body { margin: 0; padding: 0; font-family: 'Roboto', sans-serif; color: #222222; line-height: 1.5; font-weight: 300; }
header *,.bannerItem *,header input[type="search"],footer *,footer input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
img { max-width: 100%; }
.wrapper a:focus { outline: none; }
a { color: #135abc; }
a:hover, a:active { color: #135abc; }
h1, h2, h3, h4, h5, h6 { font-family: 'Roboto', sans-serif; font-weight: 400; }
h1 { font-size: 42px; }
h2 { font-size: 32px; }
h3 { font-size: 24px; }
h4 { font-size: 18px; }
h5 { font-size: 20px; }
h6 { font-size: 16px; }
.container { padding: 0 15px; }
p { font-size: 16px; margin-bottom: 20px; }
.list-unstyled,.header-top-rt > div > ul,.brands ul,#footerMenuHolder ul { list-style: none; }
/***Header***/
.logo { max-width: 740px; float: left; }
.logo img { width:100% }
.header-top-rt { float: right; margin-top: 35px; }
.social li { display: inline-block; padding: 0 4px; }
.header-top-rt ul { margin: 0; }
.search-box li a,.header-top-rt > div:nth-child(2) li a,#footerMenuHolder > ul > li:last-child > div li a { display: block; font-weight: 400; font-size: 15px; line-height: 22px; text-transform: uppercase; white-space: nowrap; color: #000; padding: 4px 0px;}
.float-right { float: right; }
.social li a { background: #1e73be; border-radius: 4px; color: #ffffff; display: inline-block; font-size: 18px; height: 36px; line-height: 36px; text-align: center; width: 36px; -moz-transition: all ease-in-out 0.3s; -ms-transition: all ease-in-out 0.3s; -o-transition: all ease-in-out 0.3s; -webkit-transition: all ease-in-out 0.3s; transition: all ease-in-out 0.3s; }
.social li:last-child { display: block; margin-top: 20px; }
.nav-collapse.collapse { background: transparent; }
.social li:last-child a { width: auto; height: auto; background: none; color: #004a94; font-size: 24px; }
.social li:last-child a:hover, .social li:last-child a:focus { background: none; color: #8ba5bf; }
.social li a:hover, .social li a:focus { background: #8ba5bf; }
.header .navbar { margin-bottom: 0; }
.navbar .container { width: 1170px; padding: 0 15px; }
.navbar .brand { display: inline-block; }
.header .navbar-inner { background: #fff; border: none; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; padding: 0; }
.navbar .brand { float: none; font-size: 13px; }
.brand object { width: 248px; height: 88px; }
.brand span { display: block; color: #222222; font-weight: 400; margin-bottom: 3px; }
.navbar .brand { padding: 0; margin: 0; }
.header .navbar .nav li a { text-align: center; padding: 8.5px 0; line-height: 25px; font-size: 18px; text-shadow: none; color: #fff; font-family: 'Roboto', sans-serif; font-weight: 400; text-decoration: none; -moz-transition: all ease 0.5s; -ms-transition: all ease 0.5s; -o-transition: all ease 0.5s; -webkit-transition: all ease 0.5s; transition: all ease 0.5s; }
.header .navbar .nav li:last-child a { border-right: none; }
.header .navbar .nav li.active a { background: transparent; color: #FFFFFF; }
.header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .footer-menu li a:hover, .footer-menu li a:focus { background: #026694; }
.header .nav-collapse .nav { margin: 0; }
ul.nav.search-box-show { display: none; visibility: hidden; }
.header .navbar .nav li.active a:hover { background: transparent; color: #FFFFFF; }
.navbar .nav > li.dropdown > a { cursor: auto; }
.navIcon .menu { display: none; }
.header .navbar .nav > li > .dropdown-menu::after, .header .navbar .nav > li > .dropdown-menu::before { display: none; }
.header .navbar .nav > li > .dropdown-menu::after, .header .navbar .nav > li > .dropdown-menu::before { display: none; }
.header .nav-collapse li:hover .dropdown-menu { display: block; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { left: 0; right: inherit; top: 34px; }
.header .dropdown-menu > li > a { background: rgb(81,121,146); }
.header .navbar .nav li .dropdown-menu > li > a { padding: 0; margin: 13px 5px; font-size: 18px; line-height: 18px; text-align: left; background: #656565; color: #fff; }
.navbar .nav > li a { text-transform: uppercase; }
.navbar .nav > li ul li a { text-transform: capitalize; }
.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.header .navbar .nav li .dropdown-menu > li:last-child a { border-bottom: none; }
.header .dropdown-menu { min-width: 200px; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { top: 40px; padding: 0px 8px 3px; border: none; border-radius: 0; background: #656565; width: auto; }
.dropdown-submenu > .dropdown-menu { border: none; padding: 0; margin: 0; }
.header .dropdown-submenu .dropdown-menu { display: none!important; }
.header .dropdown-submenu:hover .dropdown-menu { display: block!important; }
.dropdown-submenu > a::after { display: none; }
.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a { border: none; background-color: #fff; color: #004a94; border-bottom: 1px solid #f5f5f5; }
.header .dropdown-menu { min-width: 148px; width: 148px; }
.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover, .header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .navbar .nav li.dropdown.open > .dropdown-toggle, .navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav li.dropdown.open.active > .dropdown-toggle { background: transparent; color: #fff; }
[data-toggle="dropdown"] {
display: none;
}
.header .navbar .nav li .dropdown-menu > li:hover a, .header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover { background-color: #656565; color: #fff; text-decoration: underline; }
.float-left { float: left; }
.search-box input,.header-top-rt > div:nth-child(2) input,#footerMenuHolder > ul > li:last-child > div input { -moz-appearance: none; -webkit-appearance: none; appearance: none; }
.buttonsDiv li a,.header-top-rt > div:first-child li a,.header-top-rt .pull-right ul > li:first-child a { background-color: #3465a8; color: #fff; display: block; font-weight: 500; font-size: 15px; line-height: 2em; text-align: center; text-transform: uppercase; padding: 0 10px; white-space: nowrap; min-width: 114px; }
.buttonsDiv ul,.header-top-rt > div:first-child ul { margin: 0; }
.buttonsDiv li,.header-top-rt > div:first-child li { margin-bottom: 15px; }
.header-top-rt > :nth-child(2) li {margin-bottom: 15px;text-align: right; }
.header-top-rt .pull-right ul > li:first-child {float:left;}
.search-box,.header-top-rt > div:nth-child(2),#footerMenuHolder > ul > li:last-child > div { padding-left: 30px; }
.search-box input,.header-top-rt > div:nth-child(2) input,#footerMenuHolder > ul > li:last-child > div input { margin: 0; height: 30px; border-radius: 0; padding-right: 40px; }
.search-box fieldset,.header-top-rt > div:nth-child(2) fieldset,#footerMenuHolder > ul > li:last-child > div fieldset { position: relative; }
.search-box button.btn,.header-top-rt > div:nth-child(2) button.btn,#footerMenuHolder > ul > li:last-child > div button.btn { position: absolute; top: 50%; right: 0; margin: 0; background: transparent; border: none; -moz-transform: translateY(-50%); -ms-transform: translateY(-50%); -o-transform: translateY(-50%); -webkit-transform: translateY(-50%); transform: translateY(-50%); height: 100%; -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; }
.navMain { background: #515151; border-bottom: 2px solid #fff; }
.banner,.bannerItem > div:first-child { overflow: hidden; width: 100%; display: table; background: #4976b4; }
.desktop-banner img,.bannerItem > div:first-child > div:first-child img { width: 100%; height: auto; }
.desktop-banner,.bannerItem > div:first-child > div:first-child { display: table-cell; max-width: 952px; width: 62%; }
.banner-content,.bannerItem > div:first-child > div:last-child { display: table-cell; width: 38%; max-width: 488px; padding-top: 0; padding-right: 44px; text-align: center; vertical-align: middle; }
.mobile-banner,.bannerItem > div:first-child > :nth-child(2) { display: none; }
.banner-content p,.bannerItem > div:first-child > div:last-child p { font-size: 23px; color: #fff; margin-bottom: 20px; line-height: 32px; }
.banner-content .btn a,.bannerItem > div:first-child > div:last-child .btn a { border: 1px solid #fff; text-transform: uppercase; background-color: #445e87; font-size: 19px; display: inline-block; padding: 0 24px; color: #fff; font-weight: 500; font-weight: 500; -moz-text-shadow: none; -webkit-text-shadow: none; text-shadow: none; }
.banner-content .btn a,.bannerItem > div:first-child > div:last-child .btn a { text-decoration: none; }
.banner-content p.btn,.bannerItem > div:first-child > div:last-child p.btn { margin-bottom: 0; background: transparent; border-radius: 0; padding: 0; }
.content { padding-top: 32px; padding-bottom: 50px; }
.homeCols .cols h2 { font-size: 21px; line-height: 1.1em; padding: .5em; text-align: center; text-transform: uppercase; background: #eadcae; font-weight: 300; margin: 0; position: relative; }
.homeCols .cols { border: 2px solid #eadcae; }
.homeCols .cols:last-child { border: none; }
.cols.events { width: 38%; }
.tweet iframe { width: 100% !important; }
.text-uppercase,.homeCols-content > div > :last-child a,#titleBanner > div:first-Child > div > div > h1 { text-transform: uppercase; }
.cols.tweet { width: 30%; }
.eventCol p,.homeCols-content > div p { margin-bottom: 10px; }
.eventCol p .fa,.homeCols-content > div p .fa { margin-left: 5px; }
.no-margin, .eventCol p.no-margin,.homeCols-content > div > :nth-child(2) { margin: 0; }
.brands li { padding: 28px 0; text-align: center; border-bottom: 1px solid #cacaca; }
.brands li:last-child { border-bottom: none; }
.brands ul { margin: 0; padding: 0 20px; }
.events .homeCols-content { padding: 18px 18px 0; }
.eventCol h4,.homeCols-content > div h4 { margin: 0; }
.dark-clr,.homeCols-content > div > :nth-child(5) strong { color: #800000; }
.homeCols { display: table; }
.homeCols .cols { display: table-cell; float: none; vertical-align: top; }
.sep { width: 4%; display: table-cell; }
.eventCol,.homeCols-content > div { border-bottom: 1px solid #cacaca; margin-bottom: 15px; }
.eventCol:last-child,.homeCols-content > div:last-child { border-bottom: none; }
.tweet h2 a { float: right; }
/****Footer*****/
.footer { background: #656565; padding: 22px 0; }
.footerCols,#footerMenuHolder > ul { margin: 0; display: table; }
.footerCols > li,#footerMenuHolder > ul > li { display: table-cell; padding-right: 25px; }
.footerCols,#footerMenuHolder > ul { border-bottom: 1px solid #8d8d8d; }
.footerCols li, .footerCols a, .footerBtm a:hover, .footerBtm *,#footerMenuHolder > ul li,#footerMenuHolder > ul a { color: #fff; }
.footerCols h4,#footerMenuHolder > ul h4 { font-size: 15px; text-transform: uppercase; margin: 0 0 10px 0; white-space: nowrap; }
.footerCols > li ul,#footerMenuHolder > ul > li ul { margin: 0; padding: 0; }
.footerCols > li ul li,#footerMenuHolder > ul > li ul li { margin-bottom: 7px; }
.footerCols .search-box,#footerMenuHolder > ul > li:last-child > div { padding-left: 0; }
.footerCols *,#footerMenuHolder > ul * { font-weight: 400; }
.footerBtm { padding: 22px 0 16px; }
.footerBtm ul { margin: 0; }
.contactDetails li { display: inline-block; padding-right: 15px; }
.footerBtm * { font-size: 16px; font-weight: 400; }
.footerBtm p { margin-bottom: 0; }
.social { text-align: right; }
.footerBtm .social a { margin-left: 12px; display: inline-block; font-size: 22px; }
.footerBtm p { font-size: 13px; }
.footerEnd { margin-top: 30px; }
.footCol-ed:last-child { text-align: right; }
.orderMaterial a,#footerMenuHolder > ul:first-child > li:last-child > ul > li:first-child a { border: 1px solid #fff; display: inline-block; text-align: center; text-transform: uppercase; padding: 0 8px; font-weight: 500; }
.footerCols > li:last-child *,#footerMenuHolder > ul > li:last-child * { text-transform: uppercase; }
.footerCols li:last-child .search-box *,#footerMenuHolder > ul > li:last-child > div * { text-transform: capitalize; }
/****Interior Page****/
.inner-banner-img,#titleBanner > div:first-Child { background-size: auto 150px; background-repeat: no-repeat; background-position: right center; }
.green { background-color: #457657; }
.inner-banner-content,#titleBanner > div:first-Child > div > div { height: 150px; display: table; }
.inner-banner-content h1,#titleBanner > div:first-Child > div > div h1 { display: table-cell; vertical-align: middle; width: 100%; color: #fff; padding-left: 12px; font-size: 35px; font-weight: 300; line-height: 1.1em; }
.mission-img { background-image: url(../img/hdr_bc_dsk.jpg); }
.owl-theme .owl-nav.disabled + .owl-dots {
    margin-top: 10px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 30px;
    text-align: center;
}
.owl-carousel .owl-stage-outer{
    position: relative;
    overflow: hidden;
    -webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel.owl-loaded{
    display: block;
}
.owl-carousel {
    display: none;
    width: 100%;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    z-index: 1;
}
.owl-theme .owl-dots .owl-dot{
    display: inline-block;
    zoom: 1;
}
.owl-theme .owl-dots .owl-dot span {
    border-radius: 30px;
    height: 14px;
    margin: 0 7px;
    transition: opacity 0.2s ease 0s;
    width: 14px;
    border: solid 1px #fff;
    display: block;
}
.owl-theme .owl-dots .owl-dot.active span,.owl-theme .owl-dots .owl-dot:hover span
{
    background: none;
    border: solid 1px #fff;
    background: #fff none repeat scroll 0 0;
}
#s_key_all{
    width:206px!important;
}  
.upperCarousel,.lowerCarousel {
	width:240px !important;
	margin: auto !important;
}

#interiorPageCarouselSection{
    padding-top: 20px;
}