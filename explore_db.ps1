# Database exploration script
$server = "************"
$databases = @("membercentral", "platformMail", "platformQueue", "seminarweb")
$username = "nitin"
$password = "12%`$RN2@AZdFLV"

function Get-DatabaseInfo {
    param($dbName)
    
    $connectionString = "Server=$server;Database=$dbName;User Id=$username;Password=$password;"
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        Write-Host "=== Database: $dbName ===" -ForegroundColor Cyan
        
        # Get table count
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
        $tableCount = $command.ExecuteScalar()
        Write-Host "Number of tables: $tableCount" -ForegroundColor Yellow
        
        # Get all tables
        $command.CommandText = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset) | Out-Null
        
        Write-Host "Tables:" -ForegroundColor Green
        foreach ($row in $dataset.Tables[0].Rows) {
            Write-Host "  - $($row.TABLE_NAME)"
        }
        
        $connection.Close()
        Write-Host ""
        
    } catch {
        Write-Host "Failed to connect to $dbName`: $_" -ForegroundColor Red
    }
}

# Explore each database
foreach ($db in $databases) {
    Get-DatabaseInfo -dbName $db
}
