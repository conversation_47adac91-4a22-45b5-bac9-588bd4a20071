<cfoutput>
    <!doctype html>
    <html>

        <head>
            <cfinclude template="head.cfm">
        </head>

        <body>
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
				<div class="wrapper outer-width in-wrapper">

					<!--Header Start-->
						<cfinclude template="header.cfm">     
					<!--Header End-->

					<!--Banner Start-->
					<cfif application.objCMS.getZoneItemCount(zone='C',event=event)>  
						<cfif ArrayLen(event.getValue("mc_pageDefinition").pageZones['C'])>						
							<div id="titleBanner">								
								<cfloop array="#event.getValue("mc_pageDefinition").pageZones['C']#" index="local.zoneCContent"> 
									<cfif LCase(trim(local.zoneCContent.contentAttributes.contentTitle)) EQ "banner">       
										#REReplace(REReplace(local.zoneCContent.data,"<p>",""),"</p>","")#
									</cfif>    
								</cfloop> 							
							</div>
						</cfif> 
					</cfif> 
					<!--Banner End-->
					<div id="interiorPageCarousel">						
						<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>							
							<div class="hide" id="interiorPageCarouselContent">
								#application.objCMS.renderZone(zone='M',event=event)#
							</div>
						</cfif>						
					</div>	
					<!--Content Start-->
					<div class="content">
						<div class="container">
							#application.objCMS.renderZone(zone='Main',event=event)# 
						</div>
					</div>
					<!--Content End-->
				
					<!--Footer Start-->
						<cfinclude template="footer.cfm">          
					<!--Footer End-->
				</div>

				<cfinclude template="foot.cfm">
				<cfinclude template="toolBar.cfm">
			 <cfelse>
				<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
					#application.objCMS.renderZone(zone='Main',event=event)#
				</cfif>
			</cfif>
        </body>

    </html>
</cfoutput>