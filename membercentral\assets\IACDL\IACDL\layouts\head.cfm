<cfoutput>
    <meta charset="utf-8">
    <title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>		
    #application.objCMS.getBootstrapHeadHTML()#
    #application.objCMS.getResponsiveHeadHTML()#
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" href="/images/favicon.ico" sizes="16x16">
    #application.objCMS.getFontAwesomeHTML(includeVersion4Support=true)#
	<link rel="stylesheet" href="/assets/common/javascript/owlCarousel/211/owl.carousel.min.css">
    <link rel="stylesheet" href="/assets/common/javascript/owlCarousel/211/owl.theme.default.min.css">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Montserrat:wght@100;200;300;400;500;600;700;800;900&family=Roboto:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
	<link href="/fonts/NHaasGroteskDSPro-65Md/stylesheet.css" rel="stylesheet" type="text/css">
    <link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
    <link href="/css/responsive.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/css/print.css" media="print">
    <link href="/css/main.css" rel="stylesheet" type="text/css">
    #application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
</cfoutput>