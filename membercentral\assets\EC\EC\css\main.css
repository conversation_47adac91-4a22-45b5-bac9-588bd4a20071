/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");

/* Include in Editor: Start */
.TitleText{ text-transform: uppercase; font-size: 34px; font-family: 'Raleway', sans-serif; line-height: 37px; font-weight: 400; color: #000;}
.HeaderText{font-family: 'Roboto', sans-serif; font-size: 19px; margin-bottom: 0.8em; line-height: 27px; font-weight: 400;}
.BodyText{font-family: 'Roboto', sans-serif; font-size: 16px; font-weight: 300; line-height: 24px; margin-bottom: 20.8px; }
.InfoText{font-family: 'Roboto', sans-serif; font-size: 12px; font-weight: 300; line-height: 20px; margin-bottom: 16px; color: #aaa;}
/* Include in Editor: Stop */


/*STYLES NOT IN THE EDITOR: ***********************************************************************************************/
a .BodyText, .BodyText a, a{font-family:'Roboto', sans-serif; color:#0046a1;}
a:hover .BodyText, .BodyText a:hover, a:hover{ color:#006fff;}


