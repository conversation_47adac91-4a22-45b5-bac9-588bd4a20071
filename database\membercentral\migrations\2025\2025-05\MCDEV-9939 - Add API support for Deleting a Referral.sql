USE membercentral
GO

DECLARE @routeID int, @DELETE_rmID int, @PUT_rmID int;

select @routeID = routeID
from dbo.api_routes 
where route = '/referral/:api_id';

select @PUT_rmID = rmID
from dbo.api_routeMethods 
where routeID = @routeID
and method = 'PUT';

INSERT INTO dbo.api_routeMethods (routeID, method, functionName, purpose)
VALUES (@routeID, 'DELETE', 'delete', 'Deletes a Referral');

SET @DELETE_rmID = SCOPE_IDENTITY();

INSERT INTO dbo.api_userRouteMethods (userID, rmID)
SELECT userID, @DELETE_rmID
FROM dbo.api_userRouteMethods
WHERE rmID = @PUT_rmID;
GO