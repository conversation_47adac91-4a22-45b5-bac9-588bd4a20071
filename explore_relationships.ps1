# Database relationship exploration script
$server = "************"
$username = "nitin"
$password = "12%`$RN2@AZdFLV"

function Get-ForeignKeyRelationships {
    param($dbName)
    
    $connectionString = "Server=$server;Database=$dbName;User Id=$username;Password=$password;"
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        Write-Host "=== Foreign Key Relationships in $dbName ===" -ForegroundColor Cyan
        
        # Get foreign key relationships
        $command = $connection.CreateCommand()
        $command.CommandText = @"
SELECT 
    fk.name AS FK_NAME,
    OBJECT_NAME(fk.parent_object_id) AS TABLE_NAME,
    COL_NAME(fkc.parent_object_id, fkc.parent_column_id) AS COLUMN_NAME,
    OBJECT_NAME(fk.referenced_object_id) AS REFERENCED_TABLE_NAME,
    COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) AS REFERENCED_COLUMN_NAME
FROM 
    sys.foreign_keys AS fk
INNER JOIN
    sys.foreign_key_columns AS fkc
    ON fk.OBJECT_ID = fkc.constraint_object_id
ORDER BY TABLE_NAME, REFERENCED_TABLE_NAME
"@
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset) | Out-Null
        
        Write-Host "Key Relationships:" -ForegroundColor Green
        foreach ($row in $dataset.Tables[0].Rows) {
            Write-Host "  $($row.TABLE_NAME).$($row.COLUMN_NAME) -> $($row.REFERENCED_TABLE_NAME).$($row.REFERENCED_COLUMN_NAME)"
        }
        
        $connection.Close()
        Write-Host ""
        
    } catch {
        Write-Host "Failed to get relationships from $dbName`: $_" -ForegroundColor Red
    }
}

# Get relationships for membercentral database (main database)
Get-ForeignKeyRelationships -dbName "membercentral"
