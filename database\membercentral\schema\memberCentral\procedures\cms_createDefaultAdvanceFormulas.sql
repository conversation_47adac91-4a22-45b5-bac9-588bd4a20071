ALTER PROC dbo.cms_createDefaultAdvanceFormulas
@siteID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into dbo.af_advanceFormulas (afName, datePart, dateNum, adjustTerm, nextWeekday, weekNumber, siteID, isSystemOnly)
	VALUES 
		('Advance to End of Next Month', 'M', 1, 'EndPeriod', 0, null, @siteID, 1),
		('Advance to First of Next Month', 'M', 1, 'StartPeriod', 0, null, @siteID, 1),
		('Advance One Day', 'D', 1, null, 0, null, @siteID, 1),
		('Advance One Week', 'D', 7, null, 0, null, @siteID, 1),
		('Advance One Month', 'M', 1, null, 0, null, @siteID, 1),
		('Advance One Year', 'Y', 1, null, 0, null, @siteID, 1),
		('Advance to Same Day of Same Week Next Month', 'M', 1, 'SameDayWk', 0, 'Same', @siteID, 1),
		('Advance to Same Day of Last Week Next Month', 'M', 1, 'SameDayWk', 0, 'Last', @siteID, 1),
		('Advance to Same Day of 1st Week Next Month', 'M', 1, 'SameDayWk', 0, '1st', @siteID, 1),
		('Advance to Same Day of 2nd Week Next Month', 'M', 1, 'SameDayWk', 0, '2nd', @siteID, 1),
		('Advance to Same Day of 3rd Week Next Month', 'M', 1, 'SameDayWk', 0, '3rd', @siteID, 1);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
