// JavaScript Document
$('#MenuHolder ul:first').addClass('nav');
$('#MenuHolder ul:first > li > ul').each(function(){
    $(this).parent().addClass('dropdown');
});
//$('#MenuHolder ul:first > li').has('ul').children('a').attr('data-toggle','dropdown');
$('#MenuHolder ul:first > li > a').addClass('dropdown-toggle');
$('#MenuHolder ul:first > li > ul').addClass('dropdown-menu');
$('#MenuHolder').show();
$('#mobileSearchBox').html($('.header-top-rt div:nth-child(2) form').parent().html());
$('#siteBy').html($('#socialHolder div > p:last-child').wrap('<p/>').parent().html());
$('#socialHolder div > p:last-child').remove();
$("#footerMenuHolder > ul:first-child > li:last-child").append($('#FooterLinks').html());

jQuery(document).ready(function($) {

    $('.owl-slider').owlCarousel({
        loop:true,
        margin:0,
        autoplayTimeout:5000,
        autoplaySpeed: 1000,
        nav:false,
        autoplay:true,
        dots:true,
		responsive:{
			0:{
				items:1
			},
			480:{
				items:1
			},
			600:{
				items:1
			},
			1000:{
				items:1
			}
		}
        
    });
	 	

    $(".header-top-rt div:last-child").click(function(event) {
        //$(".search-mob-box").collapse('toggle');       
        if (!$('.search-mob-box').hasClass('search-open') && !$(".nav-collapse").hasClass('in')) {
            $(".nav-collapse ul.nav").addClass('search-box-show');
            $(".nav-collapse").css('height','auto');
            $('.search-mob-box').addClass('search-open');
        } 
        else {
            if(!$(".nav-collapse").hasClass('in')){
                $(".nav-collapse").css('height','0px');
            }
            $('.search-mob-box').removeClass('search-open');
            $(".nav-collapse ul.nav").removeClass('search-box-show');

        }
    }); 

	if ($(".zoneKContent").is(':visible')) {
		var zoneKContentObj = document.querySelector('.zoneKContent');
		for (var i = zoneKContentObj.children.length; i >= 0; i--) {
			zoneKContentObj.appendChild(zoneKContentObj.children[Math.random() * i | 0]);
		}
	}

	if ($(".zoneLContent").is(':visible')) {
		var zoneLContentObj = document.querySelector('.zoneLContent');
		for (var i = zoneLContentObj.children.length; i >= 0; i--) {
			zoneLContentObj.appendChild(zoneLContentObj.children[Math.random() * i | 0]);
		}
	}
	
	if($('.brands ul').length > 0){	
		if($('.zoneKContent').length > 0){
			$($('.brands ul')[0]).append('<li ><div class="upperCarousel">'+$('.zoneKContent').html()+'</div></li>');
		}
		if($('.zoneLContent').length > 0){
			$($('.brands ul')[0]).append('<li ><div class="upperCarousel">'+$('.zoneLContent').html()+'</div></li>');
		}
	}else{
		if($('.zoneKContent').length > 0){
			$('.brands').html('<ul><li class="lowerCarousel">'+$('.zoneKContent').html()+'</li></ul>');
		}
		if($('.zoneLContent').length > 0){
			$('.brands').html('<ul><li class="lowerCarousel">'+$('.zoneLContent').html()+'</li></ul>');
		}
	}
	
	$(".upperCarousel").addClass('owl-carousel').owlCarousel({
		loop:true,
		autoplay:true,
		margin:1,
		nav:false,
		singleItem:true,
		dots:false,
		responsive:{
			0:{
				items:1
			},
			480:{
				items:1
			},
			600:{
				items:1
			},
			1000:{
				items:1
			}
		}
	 
	 });
	 $(".lowerCarousel").addClass('owl-carousel').owlCarousel({
		loop:true,
		autoplay:true,
		margin:1,
		nav:false,
		singleItem:true,
		dots:false,
		responsive:{
			0:{
				items:1
			},
			480:{
				items:1
			},
			600:{
				items:1
			},
			1000:{
				items:1
			}
		}
	 
	 });

	if ($('#interiorPageCarouselContent').length != 0) {
		var _images = $('#interiorPageCarouselContent > div > div > ul > li');
		var zoneMSliderItemCount = _images.length;
		if(zoneMSliderItemCount > 0){
			$('#interiorPageCarousel').append('<div class="container"><div class="row-fluid" id="interiorPageCarouselSection"></div></div>'); 
			$('#interiorPageCarouselSection').append('<div class="zone-m-slider img-slider inner-carousel"></div>');        
			_images.each(function() {
				$('#interiorPageCarouselSection .zone-m-slider').append('<div class="item">' + $(this).html() + '</div>');
			});

			$(".zone-m-slider").addClass('owl-carousel').owlCarousel({
				loop: zoneMSliderItemCount > 3,
				autoplay:true,
				autoplayTimeout:5000,
				margin:100,
				nav:false,
				responsive:{
					0:{ items:1, margin:60, stagePadding: 55, loop: zoneMSliderItemCount > 1 },
					480:{ items:2, margin:30, stagePadding: 30, loop: zoneMSliderItemCount > 2 },
					600:{ items:2, margin:80, stagePadding: 30, loop: zoneMSliderItemCount > 2 },
					768:{ items:3, margin:40, stagePadding: 30 },
					900:{ items:3, margin:55, stagePadding: 30 },
					1000:{ items:3, margin:90, stagePadding: 50 },
					1200:{ items:3, margin:120, stagePadding: 90 }
				},onInitialize : function(elem){
					randomizeInitialSlideCustom(elem,'rightSponsorsCarousel');
				}        
			});
		}        
    } 

});

function randomizeInitialSlideCustom(owlSelector, zoneName){
    var _children = $(owlSelector.target).children();
    var slideCount = _children.length;	
    var maxIndex = slideCount-1;	
    var randomizedSlides = [];
    if(maxIndex > 0){
        var startIndex = getNewStartIndexCustom(maxIndex, zoneName);
        for (var i = startIndex; i <= maxIndex; i++) { 
            randomizedSlides.push(_children[i]);
        }
        for (var i = 0; i < startIndex; i++) { 
            randomizedSlides.push(_children[i]);
        }
        $.each( randomizedSlides, function( index, value ) {
            $(value).appendTo($(owlSelector.target));
        });
    }		
}
function getNewStartIndexCustom(maxIndex, zoneName){
    var startIndex = randomNumberFromRangeCustom(0, maxIndex);
    var startIndexKey = 'startIndex' + zoneName;
    if(localStorage[startIndexKey] != undefined ){
        if(localStorage[startIndexKey] != startIndex)
            localStorage[startIndexKey] = startIndex;
        else {
            localStorage[startIndexKey] = (startIndex + 1 > maxIndex) ? startIndex - 1 : startIndex + 1;
        }
    } else {
        localStorage[startIndexKey] = startIndex;
    }
    return localStorage[startIndexKey];
}
function randomNumberFromRangeCustom(min,max)	{
    return Math.floor(Math.random()*(max-min+1)+min);
}