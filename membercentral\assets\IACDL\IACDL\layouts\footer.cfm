<cfoutput>
	<cfset local.zoneP1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['P'],1)>
			<cfset local.zoneP1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['P'][1].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
	<cfset local.zoneQ1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
			<cfset local.zoneQ1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['Q'][1].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
	<cfset local.zoneQ2Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
			<cfset local.zoneQ2Content = trim(event.getValue("mc_pageDefinition").pageZones['Q'][2].data)>
		</cfif>
	</cfif>
	<cfset local.zoneQ3Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
			<cfset local.zoneQ3Content = trim(event.getValue("mc_pageDefinition").pageZones['Q'][3].data)>
		</cfif>
	</cfif>
	<cfset local.zoneQ4Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
			<cfset local.zoneQ4Content = trim(event.getValue("mc_pageDefinition").pageZones['Q'][4].data)>
		</cfif>
	</cfif>
	<cfset local.zoneR1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='R',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['R'],1)>
			<cfset local.zoneR1Content = trim(event.getValue("mc_pageDefinition").pageZones['R'][1].data)>
		</cfif>
	</cfif>
	<cfset local.zoneS1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='S',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['S'],1)>
			<cfset local.zoneS1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['S'][1].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<cfset local.zoneT1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='T',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['T'],1)>
			<cfset local.zoneT1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['T'][1].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<cfset local.zoneT2Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='T',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['T'],2)>
			<cfset local.zoneT2Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['T'][2].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<!-- Footer Start-->
	<footer class="footer">
		<span class="zoneP1Wrapper hide">#local.zoneP1Content#</span>
		<div class="footerBG"><span class="zoneP1Holder"></span></div>
		<!-- footer top start -->
		<div class="footerTop">
			<div class="container containerCustom">
				<div class="row-fluid d-flex">
					<div class="span5 homeCols footCol1">
						<div class="footerLogo-wrap">
							<a href="/" class="footerLogo"><span class="zoneP11Holder"></span></a>
						</div>
						<div class="footer-content-wrap footerContactHolder">
							#local.zoneQ1Content#
							<div class="mb-20px">#local.zoneQ2Content#</div>

							<div class="mb-20px">#local.zoneQ3Content#</div>

							<div class="mb-20px">#local.zoneQ4Content#</div>

							<div class="social-links">
								#local.zoneS1Content#
							</div>
						</div>
					</div>
					<div class="span3 iconlist footCol2 joinHolder">
						#local.zoneR1Content#

						<iframe src="/?pg=contact&mode=direct" id="contactIframe" frameborder="0" onload="resizeContactIframe()" height="0px" width="100%" scrolling="no"></iframe>
					</div>
				</div>
			</div>
		</div>
		<!-- footer top End -->
	</footer>
	<!-- Copyright Start -->
	<div class="copyright">
		<div class="container containerCustom">
			<div class="copyright-in">
				<div class="row-fluid">
					<div class="span12 copyrightList">
						<ul>
							<li>#local.zoneT1Content#</li>
						</ul>
						<ul>
							<li>#local.zoneT2Content#</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Copyright End -->
	<!-- Footer End-->
	<!-- Print Hedaer -->
	<div class="printFooter">
		<p>#local.zoneT1Content#</p>
	</div>
	<!-- Print Hedaer -->
</cfoutput>