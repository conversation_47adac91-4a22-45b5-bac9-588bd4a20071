# Get key table information
$server = "************"
$username = "nitin"
$password = "12%`$RN2@AZdFLV"

function Get-TableInfo {
    param($dbName, $tableName)
    
    $connectionString = "Server=$server;Database=$dbName;User Id=$username;Password=$password;"
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        Write-Host "=== Table: $tableName in $dbName ===" -ForegroundColor Cyan
        
        # Get column information
        $command = $connection.CreateCommand()
        $command.CommandText = @"
SELECT 
    c.COLUMN_NAME,
    c.DATA_TYPE,
    c.CHARACTER_MAXIMUM_LENGTH,
    c.IS_NULLABLE,
    CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 'YES' ELSE 'NO' END AS IS_PRIMARY_KEY
FROM 
    INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN (
    SELECT ku.TABLE_CATALOG, ku.TABLE_SCHEMA, ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS tc
    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS ku
      ON tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
      AND tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
) pk
  ON c.TABLE_CATALOG = pk.TABLE_CATALOG
  AND c.TABLE_SCHEMA = pk.TABLE_SCHEMA
  AND c.TABLE_NAME = pk.TABLE_NAME
  AND c.COLUMN_NAME = pk.COLUMN_NAME
WHERE c.TABLE_NAME = '$tableName'
ORDER BY c.ORDINAL_POSITION
"@
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset) | Out-Null
        
        Write-Host "Columns:" -ForegroundColor Green
        foreach ($row in $dataset.Tables[0].Rows) {
            $dataType = $row.DATA_TYPE
            if ($row.CHARACTER_MAXIMUM_LENGTH -ne [System.DBNull]::Value) {
                $dataType += "($($row.CHARACTER_MAXIMUM_LENGTH))"
            }
            $nullable = if ($row.IS_NULLABLE -eq "YES") { "NULL" } else { "NOT NULL" }
            $primaryKey = if ($row.IS_PRIMARY_KEY -eq "YES") { "PK" } else { "" }
            
            Write-Host "  - $($row.COLUMN_NAME): $dataType $nullable $primaryKey"
        }
        
        $connection.Close()
        Write-Host ""
        
    } catch {
        Write-Host "Failed to get info for $tableName in $dbName`: $_" -ForegroundColor Red
    }
}

# Get info for key tables
$keyTables = @(
    @{db="membercentral"; table="ams_members"},
    @{db="membercentral"; table="ams_groups"},
    @{db="membercentral"; table="organizations"},
    @{db="membercentral"; table="sub_subscribers"},
    @{db="membercentral"; table="tr_transactions"},
    @{db="platformMail"; table="email_messages"},
    @{db="platformQueue"; table="tblQueueItems"},
    @{db="seminarweb"; table="tblEnrollments"}
)

foreach ($table in $keyTables) {
    Get-TableInfo -dbName $table.db -tableName $table.table
}
