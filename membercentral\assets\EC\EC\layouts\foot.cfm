<!--javascript Files-->
<script src="/assets/common/javascript/owlCarousel/233/owl.carousel.min.js" type="text/javascript"></script> 
<script src="/javascript/custom.js" type="text/javascript"></script>
<script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
<cfoutput>
    <script>

        <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

            $('.header-top-rt [loggedintext]').eq(0).html($('.header-top-rt [loggedintext]').eq(0).attr('loggedintext'));
            $('.header-top-rt [loggedintext]').eq(1).html($('.header-top-rt [loggedintext]').eq(1).attr('loggedintext')); 
            $('.header-top-rt [loggedinurl]').eq(0).attr('href',$('.header-top-rt [loggedinurl]').eq(0).attr('loggedinurl'));
            $('.header-top-rt [loggedinurl]').eq(1).attr('href',$('.header-top-rt [loggedinurl]').eq(1).attr('loggedinurl')); 
           
        </cfif>

         $('##titleBanner > div > div').remove();
         $('##titleBanner > div').append("<div class='container'><div><h1>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</h1></div></div>");

    </script>
</cfoutput>