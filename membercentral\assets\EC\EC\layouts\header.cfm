<cfoutput>
    <header id="header" class="header outer-width">
        <div id="navbar-example" class="navbar navbar-static">
            <div class="navbar-inner">
                <div class="container">
                    <div class="navIcon"><a class="btn btn-navbar menu-toggle" data-toggle="collapse" data-target=".nav-collapse"><img src="/images/icn_menu_site.png" alt="image"> </a></div>
                    <div class="logo"> 
                        <a class="brand" href="/">
                            <cfif application.objCMS.getZoneItemCount(zone='A',event=event)>    
                                <cfloop array="#event.getValue("mc_pageDefinition").pageZones['A']#" index="local.zoneALContent"> 
                                    <cfif LCase(trim(local.zoneALContent.contentAttributes.contentTitle)) EQ "logo">       
                                        #REReplace(REReplace(local.zoneALContent.data,"<p>",""),"</p>","")#
                                    </cfif>    
                                </cfloop> 
                            </cfif> 
                        </a> 
                    </div>
                    <div class="header-top-rt">
                        <cfif application.objCMS.getZoneItemCount(zone='A',event=event)>    
                            <cfloop array="#event.getValue("mc_pageDefinition").pageZones['A']#" index="local.zoneARContent"> 
                                <cfif LCase(trim(local.zoneARContent.contentAttributes.contentTitle)) EQ "right block">       
                                    #local.zoneARContent.data#
                                </cfif>    
                            </cfloop> 
                        </cfif>      
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="navMain">
                    <div class="container">
                        <div class="nav-collapse collapse clearfix">
                            <div class="search-mob-box">
                                <div class="search-box float-left" id="mobileSearchBox">
                                    
                                </div>
                            </div>
                            <cfinclude template="mainNav.cfm">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /navbar- -->
    </header>
</cfoutput>