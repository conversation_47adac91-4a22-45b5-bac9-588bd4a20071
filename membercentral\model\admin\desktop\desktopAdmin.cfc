<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		local.methodToRun = this[arguments.event.getValue('mca_ta')];	

		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="SBqueue" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.downloadLogURL = buildCurrentLink(arguments.event,"downloadSBLog") & "&mode=stream">
		<cfset local.downloadQueueURL = buildCurrentLink(arguments.event,"downloadSBQueue") & "&mode=stream">
		<cfset local.dashJSON = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=dashboard&mca_jsonfunc=">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SBQueue.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="ConditionCache" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.startDate = "#month(now())#/1/#year(now())#">
		<cfset local.endDate = DateFormat(now(),"m/d/yyyy")>
		<cfset local.dashJSON = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=dashboard&mca_jsonfunc=">
		<cfset local.filterCondCacheLogResultLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getFilterCondCacheLogResult&mode=stream'>
		<cfset local.filterGroupCacheLogResultLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getFilterGroupCacheLogResult&mode=stream'>
		<cfset local.ruleConditionCountResultLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getRuleConditionCountResult&mode=stream'>
		
		<cfquery name="local.qryOrgs" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct o.orgID, o.orgCode, oi.organizationName as orgName
			from dbo.organizations o
			inner join dbo.sites s on s.orgID = o.orgID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			order by o.orgCode, oi.organizationName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_conditionCache.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="APIStatus" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			local.dashJSON = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=dashboard&mca_jsonfunc=";
			local.downloadLogURL = buildCurrentLink(arguments.event,"downloadAPIStatusLog") & "&mode=stream";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_APIStatus.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getSBStatus" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="sb_getQueueStatus">
				<cfprocresult name="local.qryStatus" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
j			<cfset local.qryStatus = QueryNew("QueueLocation, QueueName, is_activation_enabled, is_receive_enabled, is_enqueue_enabled, MsgCount, rowNum","varchar, varchar, bit, bit, bit, integer, integer")>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = arrayNew(1)>
		<cfloop query="local.qryStatus">
			<cfset local.thisElement = { }>
			<cfset local.thisElement['queuelocation'] = local.qryStatus.QueueLocation>
			<cfset local.thisElement['queuename'] = local.qryStatus.QueueName>
			<cfset local.thisElement['is_activation_enabled'] = local.qryStatus.is_activation_enabled>
			<cfset local.thisElement['is_receive_enabled'] = local.qryStatus.is_receive_enabled>
			<cfset local.thisElement['is_enqueue_enabled'] = local.qryStatus.is_enqueue_enabled>
			<cfset local.thisElement['msgcount'] = local.qryStatus.MsgCount>
			<cfset local.thisElement['showcols'] = local.qryStatus.showCols>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="activateSBQueue" access="public" output="false" returntype="string">
		<cfargument name="queue" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfswitch expression="#arguments.queue#">
				<cfcase value="LyrisCustom Service Broker">
					<cfquery name="local.qryActivate" datasource="#application.dsn.trialslyris1.dsn#">
						IF (select is_broker_enabled from lyrisCustom.sys.databases WHERE [name] = 'lyrisCustom') = 0
							ALTER DATABASE lyrisCustom set NEW_BROKER WITH ROLLBACK IMMEDIATE;
					</cfquery>
				</cfcase>
				<cfcase value="EmailTracking Service Broker">
					<cfquery name="local.qryActivate" datasource="#application.dsn.trialslyris1.dsn#">
						IF (select is_broker_enabled from emailTracking.sys.databases WHERE [name] = 'emailTracking') = 0
							ALTER DATABASE emailTracking set NEW_BROKER WITH ROLLBACK IMMEDIATE;
					</cfquery>
				</cfcase>
				<cfcase value="PlatformQueue Service Broker">
					<cfquery name="local.qryActivate" datasource="#application.dsn.platformQueue.dsn#">
						IF (select is_broker_enabled from sys.databases WHERE [name] = 'platformQueue') = 0
							ALTER DATABASE platformQueue set NEW_BROKER WITH ROLLBACK IMMEDIATE;
					</cfquery>
				</cfcase>
				<cfcase value="ClickTrackingQueue,OpenTrackingQueue">
					<cfquery name="local.qryActivate" datasource="#application.dsn.trialslyris1.dsn#">
						IF (select is_receive_enabled from sys.service_queues where [name] = '#arguments.queue#') = 0
							ALTER QUEUE trialslyris1.dbo.#arguments.queue# WITH STATUS = ON;
					</cfquery>
				</cfcase>
				<cfcase value="SendGridStatusTrackingInitiatorQueue,SendGridStatusTrackingTargetQueue,HalonProtectMsgStatusRecorderInitiatorQueue,HalonProtectMsgStatusRecorderTargetQueue">
					<cfquery name="local.qryActivate" datasource="#application.dsn.trialslyris1.dsn#">
						IF (select is_receive_enabled from emailTracking.sys.service_queues where [name] = '#arguments.queue#') = 0
							ALTER QUEUE emailTracking.dbo.#arguments.queue# WITH STATUS = ON;
					</cfquery>
				</cfcase>
				<cfdefaultcase>
					<cfquery name="local.qryActivate" datasource="#application.dsn.platformQueue.dsn#">
						IF (select is_receive_enabled from sys.service_queues where [name] = '#arguments.queue#') = 0
							ALTER QUEUE platformQueue.dbo.#arguments.queue# WITH STATUS = ON;
					</cfquery>
				</cfdefaultcase>
			</cfswitch>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getSBQueueSummary" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="up_getQueueSummary">
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocresult name="local.qryInQueueSummary" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.qryInQueueSummary = QueryNew("queueType, queueStatus, numItems, minDateAdded, offerDelete","varchar, varchar, integer, date, bit")>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = []>
		<cfloop query="local.qryInQueueSummary">
			<cfset local.thisElement = {}>
			<cfset local.thisElement['queuetype'] = local.qryInQueueSummary.queueType>
			<cfset local.thisElement['queuestatus'] = local.qryInQueueSummary.queueStatus>
			<cfset local.thisElement['numitems'] = local.qryInQueueSummary.numItems>
			<cfif datecompare(now(),local.qryInQueueSummary.minDateAdded,"s") is -1>
				<cfset local.thisElement['mindateadded'] = application.objCommon.diffDateLong(now(),local.qryInQueueSummary.minDateAdded) & " in the future">
			<cfelse>
				<cfset local.thisElement['mindateadded'] = application.objCommon.diffDateLong(local.qryInQueueSummary.minDateAdded,now())>
			</cfif>
			<cfset local.thisElement['links'] = []>
			<cfif NOT listFindNoCase("memberConditions,memberGroups",local.qryInQueueSummary.queueType)>
				<cfset local.link = { fn="download", ic="fa-light fa-download fa-lg", tx="Download" }><cfset arrayAppend(local.thisElement.links,local.link)>
			</cfif>
			<cfif local.qryInQueueSummary.offerDelete is 1>
				<cfset local.link = { fn="delete", ic="fa-light fa-trash-can fa-lg", tx="Delete" }><cfset arrayAppend(local.thisElement.links,local.link)>
			</cfif>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getMCGQueueSummary" access="public" output="false" returntype="string">
		<cfargument name="thisOrgID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfstoredproc procedure="queuemonitor_getEntriesSummary" datasource="#application.dsn.platformQueue.dsn#">
				<cfif arguments.thisOrgID neq 1>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.thisOrgID#">
				<cfelse>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
				<cfprocresult name="local.qryInQueueSummary" resultset="1">
			</cfstoredproc>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.qryInQueueSummary = QueryNew(
				"orgCode, queueType, queueStatus, processType, numEntries, numGroupedEntries, oldestEntry",
				"varchar, varchar, varchar, varchar, integer, integer, date"
			)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = { "summary": [] }>
		<cfloop query="local.qryInQueueSummary">
			<cfset local.thisElement = {}>
			<cfset local.thisElement['orgcode'] = local.qryInQueueSummary.orgCode>
			<cfset local.thisElement['queuetype'] = local.qryInQueueSummary.queueType>
			<cfset local.thisElement['queuestatus'] = local.qryInQueueSummary.queueStatus>
			<cfset local.thisElement['processtype'] = local.qryInQueueSummary.processType>
			<cfset local.thisElement['numentries'] = local.qryInQueueSummary.numEntries>
			<cfset local.thisElement['numgroupedentries'] = local.qryInQueueSummary.numGroupedEntries>
			<cfset local.thisElement['oldestentry'] = application.objCommon.diffDateLong(local.qryInQueueSummary.oldestEntry,now())>

			<cfset local.thisElement['links'] = []>
			<cfset arrayAppend(local.thisElement['links'], { fn="download", ic="fa-light fa-download fa-lg", tx="Download" })>
			<cfif arguments.thisOrgID eq 1>
				<cfset arrayAppend(local.thisElement['links'], { fn="delete", ic="fa-light fa-trash-can fa-lg", tx="Delete" })>
			</cfif>

			<cfset arrayAppend(local.strReturn.data.summary, local.thisElement)>
		</cfloop>
		
		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getMCGQueueEntries" access="public" output="false" returntype="string">
		<cfargument name="thisOrgID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfstoredproc procedure="queuemonitor_getEntries" datasource="#application.dsn.platformQueue.dsn#">
				<cfif arguments.thisOrgID neq 1>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.thisOrgID#">
				<cfelse>
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
				<cfprocresult name="local.qryInQueueRunning" resultset="1">
				<cfprocresult name="local.qryInQueueNext" resultset="2">
			</cfstoredproc>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.qryInQueueRunning = QueryNew(
				"orgCode, dateTriggered, processArea, procName, processType, timeMS, conditionCount, runType, memberCount",
				"varchar, date, varchar, varchar, varchar, integer, integer, varchar, integer"
			)>
			<cfset local.qryInQueueNext = QueryNew(
				"orgCode, dateQueued, secondsQueued, processArea, subProc, conditionCount, runType, memberCount",
				"varchar, date, varchar, varchar, varchar, integer, varchar, integer"
			)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = {  "running": [], "next": [] }>

		<cfloop query="local.qryInQueueRunning">
			<cfset local.thisElement = {}>
			<cfset local.thisElement['orgcode'] = local.qryInQueueRunning.orgCode>
			<cfset local.thisElement['datetriggered'] = dateTimeformat(local.qryInQueueRunning.dateTriggered,"m/d/yy h:nn:ss tt")>
			<cfset local.thisElement['processarea'] = local.qryInQueueRunning.processArea>
			<cfset local.thisElement['procname'] = local.qryInQueueRunning.procName>
			<cfset local.thisElement['processtype'] = local.qryInQueueRunning.processType>
			<cfset local.thisElement['timems'] = local.qryInQueueRunning.timeMS>
			<cfset local.thisElement['conditioncount'] = local.qryInQueueRunning.conditionCount>
			<cfset local.thisElement['runtype'] = local.qryInQueueRunning.runType>
			<cfset local.thisElement['membercount'] = local.qryInQueueRunning.memberCount>

			<cfset arrayAppend(local.strReturn.data.running, local.thisElement)>
		</cfloop>
		<cfloop query="local.qryInQueueNext">
			<cfset local.thisElement = {}>
			<cfset local.thisElement['orgcode'] = local.qryInQueueNext.orgCode>
			<cfset local.thisElement['datequeued'] = dateTimeformat(local.qryInQueueNext.dateQueued,"m/d/yy h:nn:ss tt")>
			<cfset local.thisElement['secondsqueued'] = local.qryInQueueNext.secondsQueued>
			<cfset local.thisElement['processarea'] = local.qryInQueueNext.processArea>
			<cfset local.thisElement['subproc'] = local.qryInQueueNext.subProc>
			<cfset local.thisElement['conditioncount'] = local.qryInQueueNext.conditionCount>
			<cfset local.thisElement['runtype'] = local.qryInQueueNext.runType>
			<cfset local.thisElement['membercount'] = local.qryInQueueNext.memberCount>

			<cfset arrayAppend(local.strReturn.data.next, local.thisElement)>
		</cfloop>	

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="SBQueueAction" access="public" output="false" returntype="string">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="action" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfswitch expression="#arguments.action#">
				<cfcase value="download">
					<cfset local.strReturn = downloadSBQueue(queue=arguments.queue, status=arguments.status)>
				</cfcase>
				<cfcase value="delete">
					<cfset local.strReturn = deleteSBQueue(queue=arguments.queue, status=arguments.status)>
				</cfcase>
			</cfswitch>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="MCGQueueAction" access="public" output="false" returntype="string">
		<cfargument name="thisOrgCode" type="string" required="true">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="processtype" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfswitch expression="#arguments.action#">
				<cfcase value="download">
					<cfset local.strReturn = downloadMCGQueue(argumentcollection=arguments)>
				</cfcase>
				<cfcase value="delete">
					<cfif arguments.thisOrgCode eq "MC">
						<cfset local.strReturn = deleteMCGQueue(argumentcollection=arguments)>
					</cfif>
				</cfcase>
			</cfswitch>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="getSBLog" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryLog1" datasource="#application.dsn.platformQueue.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select top 10 LogDate, ErrorMessage
				from dbo.sb_ServiceBrokerErrorLogs WITH(NOLOCK)
				where isVisible = 1
				order by logID desc;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfquery name="local.qryLog2" datasource="#application.dsn.trialslyris1.dsn#">
				select top 10 LogDate, ErrorMessage
				from lyrisCustom.dbo.sb_ServiceBrokerErrorLogs WITH(NOLOCK)
				order by logID desc
			</cfquery>
			<cfquery name="local.qryLog3" datasource="#application.dsn.trialslyris1.dsn#">
				select top 10 LogDate, ErrorMessage
				from emailtracking.dbo.sb_ServiceBrokerErrorLogs WITH(NOLOCK)
				order by logID desc
			</cfquery>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.qryLog1 = QueryNew("LogDate, ErrorMessage","date, varchar")>
			<cfset local.qryLog2 = QueryNew("LogDate, ErrorMessage","date, varchar")>
			<cfset local.qryLog3 = QueryNew("LogDate, ErrorMessage","date, varchar")>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = arrayNew(1)>
		<cfloop query="local.qryLog1">
			<cfset local.thisElement = { }>
			<cfset local.thisElement['logdate'] = dateformat(local.qryLog1.LogDate,'m/d/yyyy') & ' ' & timeformat(local.qryLog1.LogDate,'HH:mm:ss')>
			<cfset local.thisElement['errormessage'] = local.qryLog1.ErrorMessage>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>
		<cfloop query="local.qryLog2">
			<cfset local.thisElement = { }>
			<cfset local.thisElement['logdate'] = dateformat(local.qryLog2.LogDate,'m/d/yyyy') & ' ' & timeformat(local.qryLog2.LogDate,'HH:mm:ss')>
			<cfset local.thisElement['errormessage'] = local.qryLog2.ErrorMessage>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>
		<cfloop query="local.qryLog3">
			<cfset local.thisElement = { }>
			<cfset local.thisElement['logdate'] = dateformat(local.qryLog3.LogDate,'m/d/yyyy') & ' ' & timeformat(local.qryLog3.LogDate,'HH:mm:ss')>
			<cfset local.thisElement['errormessage'] = local.qryLog3.ErrorMessage>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>
		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="downloadMCGQueue" access="private" output="false" returntype="struct">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="processtype" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>

			<cfset local.fileName = "QueueDetails-#arguments.orgcode#-#arguments.queue#-#arguments.status#">
			<cfif len(arguments.processtype)>
				<cfset local.fileName &= "-#arguments.processtype#">
			</cfif>
			<cfset local.fileName &= ".csv">

			<cfquery name="local.qryDownload" datasource="#application.dsn.platformQueue.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @selectsql varchar(max);

				IF OBJECT_ID('tempdb..##tmpDownloadQueueData') IS NOT NULL 
					DROP TABLE ##tmpDownloadQueueData;

				<cfif arguments.queue eq "memberConditions">
					SELECT o.orgCode, 'memberConditions' as queueType, qs.queueStatus, qi.processType, qi.itemGroupUID, qi.itemID, 
						qi.memberID, qi.conditionID, qi.dateAdded, qi.dateUpdated, ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemGroupUID) as mcCSVorder 
					INTO ##tmpDownloadQueueData
					FROM platformQueue.dbo.queue_memberConditions as qi
					INNER JOIN membercentral.dbo.organizations as o on o.orgID = qi.orgID and o.orgcode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orgcode#">
					INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">
					WHERE qi.processType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.processtype#">;

					SET @selectsql = 'SELECT orgCode, queueType, queueStatus, processType, itemGroupUID, itemID, memberID, conditionID, dateAdded, dateUpdated, mcCSVorder *FROM* ##tmpDownloadQueueData';
				<cfelseif arguments.queue eq "memberGroups">
					SELECT o.orgCode, 'memberGroups' as queueType, qs.queueStatus, qi.itemGroupUID, qi.itemID, 
						qi.memberID, qi.dateAdded, qi.dateUpdated, ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemGroupUID) as mcCSVorder 
					INTO ##tmpDownloadQueueData
					FROM platformQueue.dbo.queue_memberGroups as qi
					INNER JOIN membercentral.dbo.organizations as o on o.orgID = qi.orgID and o.orgcode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orgcode#">
					INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">;

					SET @selectsql = 'SELECT orgCode, queueType, queueStatus, itemGroupUID, itemID, memberID, dateAdded, dateUpdated, mcCSVorder *FROM* ##tmpDownloadQueueData';
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				DECLARE @csvfilename varchar(400) = '#local.strFolder.folderPathUNC#\#local.fileName#';
				EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

				IF OBJECT_ID('tempdb..##tmpDownloadQueueData') IS NOT NULL 
					DROP TABLE ##tmpDownloadQueueData;
			</cfquery>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#", displayName=local.fileName, deleteSourceFile=1)>

			<cfset local.strReturn['url'] = '/tsdd/#local.stDownloadURL#'>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="downloadSBQueue" access="private" output="false" returntype="struct">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>

			<cfif len(arguments.status)>
				<cfset local.fileName =  ReReplaceNoCase("QueueDetails-#arguments.queue#-#arguments.status#.csv",'[^A-Za-z0-9_\-\.]','','ALL')>
			<cfelse>
				<cfset local.fileName = "QueueDetails-#arguments.queue#.csv">
			</cfif>

			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="up_downloadQueue">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queue#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.fileName#",'\')#">
			</cfstoredproc>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#", displayName=local.fileName, deleteSourceFile=1)>

			<cfset local.strReturn['url'] = '/tsdd/#local.stDownloadURL#'>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="deleteMCGQueue" access="private" output="false" returntype="struct">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="orgcode" type="string" required="true">
		<cfargument name="processtype" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfif arguments.queue eq "memberConditions">
				<cfquery name="local.qryDelQueue" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @queueStatusID int, @processType varchar(30) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.processtype#">, 
						@orgID int = membercentral.dbo.fn_getOrgIDFromOrgCode(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orgcode#">),
						@batchsize int = 100000, @rowcount int = 1, @loopcount int = 0;
					
					EXEC dbo.queue_getStatusIDbyType @queueType='memberConditions', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">, @queueStatusID=@queueStatusID OUTPUT;

					while @rowcount > 0 begin
						DELETE top (@batchsize) qi WITH(READPAST) 
						FROM dbo.queue_memberConditions as qi
						where orgID = @orgID 
						and qi.processType = @processType
						and qi.statusID = @queueStatusID;

						set @rowcount = @@ROWCOUNT;
						set @loopcount = @loopcount + 1;

						-- breath for .1 sec every 10 iterations
						IF @loopcount%10 = 0
							WAITFOR DELAY '00:00:00:100';
					end
				</cfquery>
			<cfelseif arguments.queue eq "memberGroups">
				<cfquery name="local.qryDelQueue" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @queueStatusID int, @batchsize int = 100000, @rowcount int = 1, @loopcount int = 0,
						@orgID int = membercentral.dbo.fn_getOrgIDFromOrgCode(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orgcode#">);
					
					EXEC dbo.queue_getStatusIDbyType @queueType='memberGroups', @queueStatus=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">, @queueStatusID=@queueStatusID OUTPUT;

					while @rowcount > 0 begin
						DELETE top (@batchsize) qi WITH(READPAST) 
						FROM dbo.queue_memberGroups as qi
						where orgID = @orgID 
						and qi.statusID = @queueStatusID;

						set @rowcount = @@ROWCOUNT;
						set @loopcount = @loopcount + 1;

						-- breath for .1 sec every 10 iterations
						IF @loopcount%10 = 0
							WAITFOR DELAY '00:00:00:100';
					end
				</cfquery>
			</cfif>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="deleteSBQueue" access="private" output="false" returntype="struct">
		<cfargument name="queue" type="string" required="true">
		<cfargument name="status" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.platformQueue.dsn#" procedure="up_clearQueue">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queue#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
			</cfstoredproc>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objerror.senderror(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="downloadSBLog" access="public" output="false" returntype="void">
		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>
		<cfset local.fileName = "ServiceBrokerErrorLog.csv">

		<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpLog') IS NOT NULL 
				DROP TABLE ##tmpLog;
			CREATE TABLE ##tmpLog (logID bigint, logDate datetime, errorMessage nvarchar(2048), messageXML xml);

			INSERT INTO ##tmpLog (logID, logDate, errorMessage, messageXML)
			SELECT LogID, LogDate, ErrorMessage, cast(MessageBody as xml) as messageXML
			FROM platformQueue.dbo.sb_ServiceBrokerErrorLogs
			WHERE isVisible = 1;

			INSERT INTO ##tmpLog (logID, logDate, errorMessage, messageXML)
			SELECT LogID, LogDate, ErrorMessage, cast(MessageBody as xml) as messageXML
			FROM LYRIS.lyrisCustom.dbo.sb_ServiceBrokerErrorLogs;

			INSERT INTO ##tmpLog (logID, logDate, errorMessage, messageXML)
			SELECT LogID, LogDate, ErrorMessage, cast(MessageBody as xml) as messageXML
			FROM LYRIS.emailTracking.dbo.sb_ServiceBrokerErrorLogs;


			DECLARE @selectsql varchar(max) = '
				SELECT LogID, LogDate, ErrorMessage, messageXML, ROW_NUMBER() OVER(order by LogDate DESC) as mcCSVorder 
				*FROM* ##tmpLog';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.fileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpLog') IS NOT NULL 
				DROP TABLE ##tmpLog;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#", displayName=local.fileName, deleteSourceFile=1)>
	</cffunction>

	<cffunction name="truncateSBLog" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryTruncate" datasource="#application.dsn.platformQueue.dsn#">
				update dbo.sb_ServiceBrokerErrorLogs
				set isVisible = 0
				where isVisible = 1
			</cfquery>
			<cfquery name="local.qryTruncate" datasource="#application.dsn.trialslyris1.dsn#">
				truncate table lyrisCustom.dbo.sb_ServiceBrokerErrorLogs

				truncate table EmailTracking.dbo.sb_ServiceBrokerErrorLogs
			</cfquery>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="refreshOrgConditions" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="refreshAction" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryRepopulate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @refreshAction varchar(30);
					set @refreshAction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.refreshAction#">;

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					<cfif arguments.orgID gt 0>
						INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
						VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">, null, null);

						EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type=@refreshAction;
					<cfelse>
						declare @orgID int;
						select distinct @orgID = min(o.orgID) 
						from dbo.organizations o 
						inner join dbo.sites s on s.orgID = o.orgID
						inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
						inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID;

						while @orgID is not null BEGIN
							INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
							VALUES (@orgID, null, null);

							EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type=@refreshAction;
			
							TRUNCATE TABLE ##tblMCQRun;

							select distinct @orgID = min(o.orgID) 
							from dbo.organizations o 
							inner join dbo.sites s on s.orgID = o.orgID
							inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
							inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
							where o.orgID > @orgID;
						END
					</cfif>

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="refreshWebsiteConfFiles" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset CreateObject("component","model.system.platform.rewrites").refreshAllConfFiles(siteID=arguments.siteID)>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="disableBetaAccess" access="public" output="false" returntype="string">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset CreateObject("component","model.admin.website.websiteAdmin").disableAllBetaAccess(siteCode=arguments.siteCode)>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="checkSiteDirectories" access="public" output="false" returntype="string">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset application.objSiteInfo.markSiteDirectoriesNotChecked(siteCode=arguments.siteCode)>
			<cfset application.objSiteInfo.checkSiteDirectories(siteCode=arguments.siteCode)>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="createAppDirectories" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset application.objSiteInfo.markAppDirectoriesNotChecked()>
			<cfset application.objSiteInfo.createAppDirectories()>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="triggerClusterWideResetAppVars" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset application.objPlatform.triggerClusterWideResetAppVars()>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="triggerClusterWideReloadSiteInfo" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset application.objSiteInfo.triggerClusterWideReload()>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="clearAdhocMemory" access="public" output="false" returntype="string">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.clearAdhocMemory">
				exec master.dbo.sp_freeAdHocMemory;
			</cfquery>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="reprocessAllPrints" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cache_perms_processAllPrints">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			</cfstoredproc>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="filterCondCacheLogResult" access="public" output="false" returntype="string">
		<cfargument name="procName" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfset arguments.dateFrom = replace(arguments.dateFrom,"- ","")>
			<cfset arguments.dateTo = replace(arguments.dateTo,"- ","")>
			<cfquery name="local.qryCacheLogData" datasource="#application.dsn.platformstatsMC.dsn#">
				SELECT procname, avg(timeMS) as avgMS,min(timeMS) as minMS, max(timeMS) as maxMS, stdev(timeMS) as standardDeviation, count(logID) as numRuns, sum(timeMS) as sumMS
				FROM dbo.cache_conditionsLogRunProcs WITH(NOLOCK)
				WHERE 1=1
				<cfif len(trim(arguments.procName))>
					and procname LIKE <cfqueryparam value="%#arguments.procName#%" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(trim(arguments.dateFrom))>
					and logDate >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and logDate <= <cfqueryparam value="#arguments.dateTo#" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY procname
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="ProcName">
						ORDER BY procname asc
					</cfcase>
					<cfcase value="Avg">
						ORDER BY avg(timeMS) desc
					</cfcase>
					<cfcase value="NumRuns">
						ORDER BY count(logID) desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY sumMS desc
					</cfdefaultcase>
				</cfswitch>
			</cfquery>

			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryCacheLogData">
				<cfset local.thisElement = { }>
				<cfset local.thisElement['procname'] = local.qryCacheLogData.procname>
				<cfset local.thisElement['avgMS'] = local.qryCacheLogData.avgMS>
				<cfset local.thisElement['minMS'] = local.qryCacheLogData.minMS>
				<cfset local.thisElement['maxMS'] = local.qryCacheLogData.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryCacheLogData.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryCacheLogData.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryCacheLogData.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>
	
	<cffunction name="getAPITestingStatus" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryAPITestingStatus" datasource="#application.dsn.platformstatsMC.dsn#">
				select top 40 testDate, testCFC, testName, results
				from dbo.apilog_testing
				order by testDate desc;
			</cfquery>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.qryAPITestingStatus = QueryNew("testDate, testCFC, testName, results","date, varchar, varchar, varchar")>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = arrayNew(1)>
		<cfloop query="local.qryAPITestingStatus">
			<cfset local.thisElement = { }>
			<cfset local.thisElement['testdate'] = dateformat(local.qryAPITestingStatus.testDate,'m/d/yyyy') & ' ' & timeformat(local.qryAPITestingStatus.testDate,'HH:mm:ss')>
			<cfset local.thisElement['testcfc'] = local.qryAPITestingStatus.testCFC>
			<cfset local.thisElement['testname'] = local.qryAPITestingStatus.testName>
			<cfset local.thisElement['results'] = local.qryAPITestingStatus.results>
			<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
		</cfloop>
		
		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>
	
	<cffunction name="downloadAPIStatusLog" access="public" output="false" returntype="void">
		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder()>
		<cfset local.fileName = "APITestingStatusLog.csv">

		<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpLog') IS NOT NULL 
				DROP TABLE ##tmpLog;
			CREATE TABLE ##tmpLog (apilogID int, testDate datetime, testCFC varchar(100), testName varchar(50), results varchar(max));

			INSERT INTO ##tmpLog (apilogID, testDate, testCFC, testName, results)
			SELECT apilogID, testDate, testCFC, testName, results
			FROM platformStatsMC.dbo.apilog_testing
			ORDER BY testDate desc;

			DECLARE @selectsql varchar(max) = '
				SELECT apilogID, testDate, testCFC, testName, results, ROW_NUMBER() OVER(order by testDate DESC) as mcCSVorder 
				*FROM* ##tmpLog';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.fileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpLog') IS NOT NULL 
				DROP TABLE ##tmpLog;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.fileName#", displayName=local.fileName, deleteSourceFile=1)>
	</cffunction>

	<cffunction name="listScheduledTasks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.viewScheduledTaskHistoryLogURL = buildCurrentLink(arguments.event,"viewScheduledTaskHistoryLog") & "&mode=direct">
		<cfset local.editScheduledTaskURL = buildCurrentLink(arguments.event,"editScheduledTask") & "&mode=direct">
		<cfset local.listScheduledTasksLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getScheduledTasks&mode=stream'>
		<cfset local.listScheduledTaskLogsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getExecutionList&mode=stream'>
		<cfset local.fDateStartedFrom = dateFormat(dateAdd("d",-5,now()), "m/d/yyyy")>
		<cfset local.fDateStartedTo = dateFormat(now(), "m/d/yyyy")>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTaskEngines">
			select engineID, engine
			from dbo.scheduledTaskEngines
			order by engine
		</cfquery>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTaskEnvs">
			select environmentID, environmentName
			from dbo.platform_environments
			order by environmentID
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_scheduledTasks.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewScheduledTaskHistoryLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.taskID = arguments.event.getValue('taskID',0)>
		<cfset local.listScheduledTaskLogsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getScheduledTaskLogs&taskID=#local.taskID#&mode=stream'>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTask">
			select st.name, st.taskCFC, st.nextRunDate, 
				cast(st.interval as varchar(5)) + ' ' + case when st.interval = 1 then stt.singular else stt.name end as taskInterval
			from dbo.scheduledTasks as st
			inner join dbo.scheduledTaskIntervalTypes as stt on stt.intervalTypeID = st.intervalTypeID
			where st.taskid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.taskID#">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_scheduledTaskLogs.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editScheduledTask" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.taskID = arguments.event.getValue('taskID',0)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTask">
			select taskID, name, siteResourceID, taskCFC, nextRunDate, interval, engineID, intervalTypeID, siteid, taskTimeoutMinutes, 
				pausable, numZeroItemCounts, periodStartDate, periodEndDate, periodDateAFID, isPaused, isRunning
			from dbo.scheduledTasks 
			where taskid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.taskID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTaskEnvSelected">
			select environmentID
			from dbo.scheduledTaskEnvironments 
			where taskid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.taskID#">
		</cfquery>
		<cfset local.envSelected = valueList(local.qryScheduledTaskEnvSelected.environmentID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTaskEngines">
			select engineID, engine
			from dbo.scheduledTaskEngines
			order by engine
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTaskEnvs">
			select environmentID, environmentName
			from dbo.platform_environments
			order by case environmentName
				when 'localDevelopment' then 1
				when 'test' then 2
				when 'newtest' then 3
				when 'development' then 4
				when 'newdevelopment' then 5
				when 'beta' then 6
				when 'newbeta' then 7
				when 'production' then 8
				else 9
				end
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduledTaskIntervalTypes">
			select intervalTypeID, name
			from dbo.scheduledTaskIntervalTypes
			order by name
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllAFs">
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			ORDER BY afName
		</cfquery>

		<cfset local.qrySites = createObject("component","desktop").getSites()>

		<cfset local.strSchedTaskFields = createObject("component","model.admin.common.modules.siteResourceFields.siteResourceFields").manageFields(
			siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), orgCode=arguments.event.getValue('mc_siteInfo.orgCode'),
			siteResourceID=local.qryScheduledTask.siteResourceID, initTableOnLoad=false)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"saveScheduledTask") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_scheduledTask.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveScheduledTask" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSiteResourceFields = createObject("component","model.admin.common.modules.siteResourceFields.siteResourceFields")>

		<cfset local.qryFields = local.objSiteResourceFields.getSiteResourceFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=arguments.event.getValue('schedTaskSiteResourceID',0))>

		<cfset local.strCustomFields = structNew()>
		<cfif local.qryFields.recordCount>
			<cfloop query="local.qryFields">
				<cfif arguments.event.valueExists('mcsrf_#local.qryFields.columnID#')>
					<cfset local.tmpStr = { columnName=local.qryFields.columnName, displayTypeCode=local.qryFields.displayTypeCode, dataTypeCode=local.qryFields.dataTypeCode,
						value=arguments.event.getTrimValue('mcsrf_#local.qryFields.columnID#') }>
					<cfset structInsert(local.strCustomFields,local.qryFields.columnID,local.tmpStr)>
				</cfif>
			</cfloop>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateScheduledTask">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				declare @taskID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskID',0)#">;
				declare @pausable bit;
				select @pausable = pausable from dbo.scheduledTasks where taskID = @taskID;

				BEGIN TRAN;
					UPDATE dbo.scheduledTasks 
					SET name = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('taskName','')#">, 
						taskCFC = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('taskCFC','')#">,
						siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskSite',1)#">, 
						<cfif len(arguments.event.getValue('nextRunDate',''))>
							nextRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('nextRunDate'),' - ',' '))#">, 
						</cfif>
						<cfif val(arguments.event.getValue('taskInterval',0)) gt 0 and val(arguments.event.getValue('taskIntervalType',0)) gt 0>
							interval = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('taskInterval'))#">, 
							intervalTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('taskIntervalType'))#">, 
						</cfif>
						<cfif val(arguments.event.getValue('taskTimeOutInMins',0)) gt 0>
							taskTimeoutMinutes = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('taskTimeOutInMins'))#">,
						</cfif>
						<cfif arguments.event.getValue('notRunning',0) is 1>
							isRunning = 0,
						</cfif>
						<cfif arguments.event.getValue('taskRunMode','') eq 'period'
								AND len(arguments.event.getValue('periodStartDate',''))
								AND len(arguments.event.getValue('periodEndDate',''))
								AND val(arguments.event.getValue('periodDateAFID',0)) GT 0>
							periodStartDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('periodStartDate'),' - ',' '))#">,
							periodEndDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('periodEndDate'),' - ',' '))#">,
							periodDateAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('periodDateAFID')#">, 
						<cfelse>
							periodStartDate = NULL,
							periodEndDate = NULL,
							periodDateAFID = NULL,
						</cfif>
						engineID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskEngine',0)#">
					WHERE taskid = @taskID;

					IF @pausable = 1
						UPDATE dbo.scheduledTasks
						SET numZeroItemCounts = <cfif val(arguments.event.getValue('taskNumZeroItemCounts',0)) gt 0>
												<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskNumZeroItemCounts')#">
											<cfelse>
												0
											</cfif>,
							isPaused = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('taskPaused',0)#">
						where taskid = @taskID;						

					delete from dbo.scheduledTaskEnvironments
					where taskID = @taskID;

					INSERT INTO dbo.scheduledTaskEnvironments (taskID, environmentID)
					SELECT @taskID, environmentID
					FROM dbo.platform_environments 
					where environmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('taskEnv',0)#" list="true">);
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif structCount(local.strCustomFields)>
			<cfset local.objSiteResourceFields.saveFieldValues(siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
					siteResourceID=arguments.event.getValue('schedTaskSiteResourceID',0), strCustomFields=local.strCustomFields)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.reloadScheduledTaskTable();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listEventCertLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCerts">
			select crd.certificateID, s.sitecode, crd.certFileName
			from dbo.crd_certificates as crd
			inner join dbo.sites as s on s.siteID = crd.siteID
			<cfif local.siteCode neq "MC">
				and s.siteCode = <cfqueryparam value="#local.siteCode#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			order by s.sitecode, crd.certFileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfif local.siteCode neq "MC" and local.qryCerts.recordCount eq 0>
				<h4>Event Certificate Logs</h4>
				<div class="alert alert-warning">Currently, there are no event certificates present on this site.</div>
			<cfelse>
				<cfinclude template="dsp_eventCerts.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="filterEventCertsLogResult" access="public" output="false" returntype="string">
		<cfargument name="thisSiteCode" type="string" required="true">
		<cfargument name="certificateIDList" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryEventCertsLogData" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @tblCerts TABLE (certificateID int PRIMARY KEY, certName varchar(43));

				INSERT INTO @tblCerts (certificateID, certName)
				select crd.certificateID,
					<cfif arguments.thisSiteCode eq "MC">
						s.sitecode + ' - ' + crd.certFileName
					<cfelse>
						crd.certFileName
					</cfif>
				from membercentral.dbo.crd_certificates as crd
				inner join membercentral.dbo.sites as s on s.siteID = crd.siteID
				<cfif arguments.thisSiteCode neq "MC">
					and s.siteCode = <cfqueryparam value="#arguments.thisSiteCode#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				<cfif ListLen(arguments.certificateIDList)>
					where crd.certificateID IN (<cfqueryparam value="#arguments.certificateIDList#" cfsqltype="CF_SQL_INTEGER" list="true">)
				</cfif>;

				SELECT crd.certificateID, crd.certName, avg(timeMS) as avgMS, count(logID) as numRuns, sum(timeMS) as sumMS
				FROM dbo.ev_certificateLog as cl
				INNER JOIN @tblCerts as crd on crd.certificateID = cl.certificateID
				WHERE 1=1
				<cfif len(trim(arguments.dateFrom))>
					and cl.dateStarted >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and cl.dateStarted <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY crd.certificateID, crd.certName
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="cert">
						ORDER BY crd.certName asc
					</cfcase>
					<cfcase value="Avg">
						ORDER BY avg(timeMS) desc
					</cfcase>
					<cfcase value="NumRuns">
						ORDER BY count(logID) desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY avg(timeMS) desc
					</cfdefaultcase>
				</cfswitch>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryEventCertsLogData">
				<cfset local.thisElement = { }>
				<cfset local.thisElement['certID'] = local.qryEventCertsLogData.certificateID>
				<cfset local.thisElement['cert'] = local.qryEventCertsLogData.certName>
				<cfset local.thisElement['avgMS'] = local.qryEventCertsLogData.avgMS>
				<cfset local.thisElement['numRuns'] = local.qryEventCertsLogData.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryEventCertsLogData.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="listSWCertLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.SWCertLogsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getSWCertLogs&mode=stream'>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCerts">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT DISTINCT s.sitecode, l.certFileName
			FROM platformStatsMC.dbo.sw_CertificateLog as l
			INNER JOIN seminarWeb.dbo.tblEnrollments AS e ON e.enrollmentID = l.enrollmentID
			INNER JOIN seminarWeb.dbo.tblParticipants AS p ON p.participantID = e.participantID
			INNER JOIN dbo.sites as s on s.siteCode = p.orgCode
			<cfif local.siteCode neq "MC">
				and s.siteCode = <cfqueryparam value="#local.siteCode#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			ORDER BY s.sitecode, l.certFileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfif local.siteCode neq "MC" AND local.qryCerts.recordCount eq 0>
				<cfoutput>
					<div>The tool you are trying to access is not supported on this site.</div>
				</cfoutput>
			<cfelse>
				<cfinclude template="dsp_SWCerts.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listSQLJobs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.viewSQLJobExecutionChartURL = buildCurrentLink(arguments.event,"viewSQLJobExecutionChartURL") & "&mode=direct">

		<cfquery datasource="#application.dsn.platformStatsMC.dsn#" name="local.qrySQLProcs">
			select distinct jrtl.procName 
			from dbo.job_runtimeLog AS jrtl
			order by jrtl.procname;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_SQLJobStatistics.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSQLJobExecutionChartURL" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.procName = arguments.event.getValue('procName','')>
		<cfset local.dateFrom = arguments.event.getValue('dateFrom','')>
		<cfset local.dateTo = arguments.event.getValue('dateTo','')>

		<cfif len(local.procName) and len(local.dateFrom) and len(local.dateTo)>
			<cfquery name="local.qrySQLStatistics" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				SELECT CAST(logDate AS DATE) AS logDate, MAX(timeMS) AS timeMS
				FROM dbo.job_runtimeLog
				WHERE procname = <cfqueryparam value="#local.procName#" cfsqltype="CF_SQL_VARCHAR">
				AND timeMS >= 0
				<cfif len(trim(local.dateFrom))>
					AND logDate >= <cfqueryparam value="#local.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(local.dateTo))>
					AND logDate <= <cfqueryparam value="#local.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY CAST(logDate AS DATE)
				ORDER BY logDate;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.arrOriginalEntries = []>
			<cfloop query="local.qrySQLStatistics">
				<cfset arrayAppend(local.arrOriginalEntries, { logDate:dateFormat(local.qrySQLStatistics.logDate,"dd mmm yyyy"), timeMS:val(local.qrySQLStatistics.timeMS) })>
			</cfloop>

			<!--- perform systematic sampling: to reduce the number of entries to a manageable size for the bar chart while preserving the trend --->
			<cfset local.totalEntries = arrayLen(local.arrOriginalEntries)>
			<cfset local.targetEntries = 60>
			<cfset local.stepSize = local.totalEntries / local.targetEntries>
			<cfset local.indices = []>

			<cfset local.arrSampledEntries = []>
			<cfif local.totalEntries gt local.targetEntries>
				<cfloop from="1" to="#local.targetEntries#" index="local.i">
					<cfset arrayAppend(indices, round((local.i - 1) * local.stepSize) + 1)>
				</cfloop>

				<cfloop from="1" to="#arrayLen(local.indices)#" index="local.i">
					<cfset local.index = indices[local.i]>
					<cfset arrayAppend(local.arrSampledEntries, { logDate:local.arrOriginalEntries[local.index].logDate, timeMS:local.arrOriginalEntries[local.index].timeMS })>
				</cfloop>
			<cfelse>
				<cfset local.arrSampledEntries = local.arrOriginalEntries>
			</cfif>

			<cfset local.strChartData = { "arrcategories": [], "arrdata": [] }>
			<cfset local.arrData = []>
			<cfloop array="#local.arrSampledEntries#" index="local.thisEntry">
				<cfset arrayAppend(local.strChartData.arrcategories, local.thisEntry.logDate)>
				<cfset arrayAppend(local.arrData, local.thisEntry.timeMS)>
			</cfloop>

			<cfset local.strChartData.arrdata = [{ "data":local.arrData }]>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_SQLJobStatistics_execChart.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "Invalid Data">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listPMIStatistics" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
	
		<cfquery name="local.qryOrgs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct o.orgID, o.orgCode, oi.organizationName as orgName
			from dbo.organizations o
			inner join dbo.sites s on s.orgID = o.orgID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			order by o.orgCode, oi.organizationName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.platformStatsMC.dsn#" name="local.qryPMIProcs">
			select distinct ls.procName 
			from dbo.ams_memberImportLogStats AS ls
			<cfif local.siteCode neq "MC">
				inner join dbo.ams_memberImportLog as l on l.logID = ls.logID
					and l.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			order by ls.procname;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_PMIStatistics.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="filterPMIStatisticsResult" access="public" output="false" returntype="string">
		<cfargument name="thisOrgID" type="numeric" required="true">
		<cfargument name="thisSiteCode" type="string" required="true">
		<cfargument name="procName" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="memberCountFrom" type="string" required="true">
		<cfargument name="memberCountTo" type="string" required="true">
		<cfargument name="orgID" type="string" required="true">
		<cfargument name="splitByOrgcode" type="boolean" required="true">
		<cfargument name="orderBy" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfif arguments.thisSiteCode neq "MC">
			<cfset arguments.orgID = arguments.thisOrgID>
		</cfif>

		<cftry>
			<cfquery name="local.qryPMIStatistics" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#val(arguments.orgID)#" cfsqltype="CF_SQL_INTEGER">;

				SELECT ls.procName, avg(l.numSubmitted) as avgNumSubmitted, avg(ls.timeMS) as avgMS, min(ls.timeMS) as minMS, max(ls.timeMS) as maxMS, stdev(ls.timeMS) as standardDeviation, count(ls.statsID) as numRuns, sum(ls.timeMS) as sumMS
					<cfif arguments.splitByOrgcode>
						, isnull(o.orgcode,'N/A') as orgcode
					</cfif>
				FROM dbo.ams_memberImportLog as l
				INNER JOIN dbo.ams_memberImportLogStats as ls on ls.logID = l.logID
				<cfif arguments.splitByOrgcode>
					inner join membercentral.dbo.organizations o
						on o.orgID = l.orgID
				</cfif>
				WHERE 1=1
				<cfif len(arguments.procName)>
					and ls.procName = <cfqueryparam value="#arguments.procName#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(trim(arguments.dateFrom))>
					and ls.logDate >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and ls.logDate <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				<cfif val(arguments.memberCountFrom) gt 0>
					and l.numSubmitted >= <cfqueryparam value="#arguments.memberCountFrom#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif val(arguments.memberCountTo) gt 0>
					and l.numSubmitted <= <cfqueryparam value="#arguments.memberCountTo#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				<cfif val(arguments.orgID) gt 0>
					and l.orgID = @orgID
				</cfif>
				GROUP BY ls.procName <cfif arguments.splitByOrgcode>, o.orgcode</cfif>

				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="Proc">
						ORDER BY ls.procName asc
					</cfcase>
					<cfcase value="Avg">
						ORDER BY avgMS desc
					</cfcase>
					<cfcase value="Max">
						ORDER BY maxMS desc
					</cfcase>
					<cfcase value="Min">
						ORDER BY minMS desc
					</cfcase>
					<cfcase value="NumRuns">
						ORDER BY numRuns desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY avgMS desc
					</cfdefaultcase>
				</cfswitch>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryPMIStatistics">
				<cfset local.thisElement = { }>
				<cfif arguments.splitByOrgcode>
					<cfset local.thisElement['orgcode'] = local.qryPMIStatistics.orgcode>
				<cfelse>
					<cfset local.thisElement['orgcode'] = "">
				</cfif>
				<cfset local.thisElement['procName'] = local.qryPMIStatistics.procName>
				<cfset local.thisElement['avgNumSubmitted'] = local.qryPMIStatistics.avgNumSubmitted>
				<cfset local.thisElement['avgMS'] = local.qryPMIStatistics.avgMS>
				<cfset local.thisElement['minMS'] = local.qryPMIStatistics.minMS>
				<cfset local.thisElement['maxMS'] = local.qryPMIStatistics.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryPMIStatistics.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryPMIStatistics.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryPMIStatistics.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="filterSQLStatisticsResult" access="public" output="false" returntype="string">
		<cfargument name="procName" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qrySQLStatistics" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT jrtl.procName, avg(jrtl.timeMS) as avgMS, min(jrtl.timeMS) as minMS, max(jrtl.timeMS) as maxMS, 
					stdev(jrtl.timeMS) as standardDeviation, COUNT(jrtl.logID) as numRuns, sum(jrtl.timeMS) as sumMS
				FROM dbo.job_runtimeLog as jrtl
				WHERE 1=1
				<cfif len(arguments.procName)>
					and jrtl.procName = <cfqueryparam value="#arguments.procName#" cfsqltype="CF_SQL_VARCHAR">
				</cfif>
				<cfif len(trim(arguments.dateFrom))>
					and jrtl.logDate >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and jrtl.logDate <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>
				GROUP BY jrtl.procName 
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="Proc">
						ORDER BY jrtl.procName asc
					</cfcase>
					<cfcase value="Avg">
						ORDER BY avgMS desc
					</cfcase>
					<cfcase value="Max">
						ORDER BY maxMS desc
					</cfcase>
					<cfcase value="Min">
						ORDER BY minMS desc
					</cfcase>
					<cfcase value="Std">
						ORDER BY standardDeviation desc
					</cfcase>
					<cfcase value="NumRuns">
						ORDER BY numRuns desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY avgMS desc
					</cfdefaultcase>
				</cfswitch>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qrySQLStatistics">
				<cfset local.thisElement = { }>
				<cfset local.thisElement['procName'] = local.qrySQLStatistics.procName>
				<cfset local.thisElement['avgMS'] = local.qrySQLStatistics.avgMS>
				<cfset local.thisElement['minMS'] = local.qrySQLStatistics.minMS>
				<cfset local.thisElement['maxMS'] = local.qrySQLStatistics.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qrySQLStatistics.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qrySQLStatistics.numRuns>
				<cfset local.thisElement['sumMS'] = local.qrySQLStatistics.sumMS>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="filterARCacheLogsResult" access="public" output="false" returntype="string">
		<cfargument name="thisOrgCode" type="string" required="true">
		<cfargument name="orgID" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryARCacheLogs" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpLogData') IS NOT NULL 
					DROP TABLE ##tmpLogData;
				CREATE TABLE ##tmpLogData (orgID int, orgcode varchar(10), organizationName varchar(100), logID int, logDate date, asOfDate date, timeMS int);

				INSERT INTO ##tmpLogData (orgID, orgcode, organizationName, logID, logDate, asOfDate, timeMS)
				SELECT bartl.orgID, o.orgcode, oi.organizationName, bartl.logID, cast(bartl.logDate as date), cast(bartl.asOfDate as date), bartl.timeMS
				FROM dbo.tr_baseARRuntimeLog bartl			
				inner join membercentral.dbo.organizations o ON bartl.orgID = o.orgID
				<cfif arguments.thisOrgCode NEQ "MC">
					and o.orgCode = <cfqueryparam value="#arguments.thisOrgCode#" cfsqltype="CF_SQL_VARCHAR">
				<cfelseif len(arguments.orgID)>
					and o.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
				where bartl.sumBy = 'arcache'
				<cfif len(trim(arguments.dateFrom))>
					and bartl.logDate >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and bartl.logDate <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>;

				SELECT tmp.orgID, tmp.orgcode, tmp.organizationName, avg(tmp.timeMS) as avgMS, min(tmp.timeMS) as minMS, 
					max(tmp.timeMS) as maxMS, stdev(tmp.timeMS) as standardDeviation, COUNT(tmp.logID) as numRuns, sum(tmp.timeMS) as sumMS,
					(
					select [log].logDate as ld, [log].asOfDate as aod, [log].timeMS as ms
					FROM ##tmpLogData as [log]
					WHERE [log].orgID = tmp.orgID
					ORDER BY [log].logDate DESC, [log].asOfDate DESC
					FOR XML AUTO, ROOT('logs')
					) as xmlDetails
				FROM ##tmpLogData as tmp
				GROUP BY tmp.orgID, tmp.orgcode, tmp.organizationName
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="Avg">
						ORDER BY avgMS desc
					</cfcase>
					<cfcase value="Max">
						ORDER BY maxMS desc
					</cfcase>
					<cfcase value="Min">
						ORDER BY minMS desc
					</cfcase>
					<cfcase value="Std">
						ORDER BY standardDeviation desc
					</cfcase>
					<cfcase value="OrgName">
						ORDER BY tmp.orgcode desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY avgMS desc
					</cfdefaultcase>
				</cfswitch>;

				IF OBJECT_ID('tempdb..##tmpLogData') IS NOT NULL 
					DROP TABLE ##tmpLogData;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryARCacheLogs">
				<cfset local.arrLogs = XMLParse(local.qryARCacheLogs.xmlDetails).xmlRoot.xmlChildren.map(function(item){
					return arguments.item.XMLAttributes;
 					})>

				<cfset local.thisElement = {}>
				<cfset local.thisElement['orgcode'] = local.qryARCacheLogs.orgcode>
				<cfset local.thisElement['orgName'] = local.qryARCacheLogs.organizationName>
				<cfset local.thisElement['avgMS'] = local.qryARCacheLogs.avgMS>
				<cfset local.thisElement['minMS'] = local.qryARCacheLogs.minMS>
				<cfset local.thisElement['maxMS'] = local.qryARCacheLogs.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryARCacheLogs.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryARCacheLogs.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryARCacheLogs.sumMS>
				<cfset local.thisElement['arrlogs'] = local.arrLogs>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="filterMemGrpHistoryLogsResult" access="public" output="false" returntype="string">
		<cfargument name="thisOrgCode" type="string" required="true">
		<cfargument name="orgID" type="string" required="true">
		<cfargument name="dateFrom" type="string" required="true">
		<cfargument name="dateTo" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryMemGrpHistoryLogs" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpLogData') IS NOT NULL 
					DROP TABLE ##tmpLogData;
				CREATE TABLE ##tmpLogData (orgID int, orgcode varchar(10), organizationName varchar(100), logID int, datestarted date, timeMS int);

				INSERT INTO ##tmpLogData (orgID, orgcode, organizationName, logID, datestarted, timeMS)
				SELECT cmghl.orgID, o.orgcode, oi.organizationName, cmghl.logID, cast(cmghl.datestarted as date), cmghl.timeMS
				FROM dbo.cache_members_groups_historyLog cmghl			
				inner join membercentral.dbo.organizations o ON cmghl.orgID = o.orgID
				<cfif arguments.thisOrgCode NEQ "MC">
					and o.orgCode = <cfqueryparam value="#arguments.thisOrgCode#" cfsqltype="CF_SQL_VARCHAR">
				<cfelseif len(arguments.orgID)>
					and o.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
				where 1 =1 
				<cfif len(trim(arguments.dateFrom))>
					and cmghl.datestarted >= <cfqueryparam value="#arguments.dateFrom#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(trim(arguments.dateTo))>
					and cmghl.datestarted <= <cfqueryparam value="#arguments.dateTo# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">
				</cfif>;

				SELECT tmp.orgID, tmp.orgcode, tmp.organizationName, avg(tmp.timeMS) as avgMS, min(tmp.timeMS) as minMS, 
					max(tmp.timeMS) as maxMS, stdev(tmp.timeMS) as standardDeviation, COUNT(tmp.logID) as numRuns, sum(tmp.timeMS) as sumMS,
					(
					select [log].datestarted as ld, [log].timeMS as ms
					FROM ##tmpLogData as [log]
					WHERE [log].orgID = tmp.orgID
					ORDER BY [log].datestarted DESC
					FOR XML AUTO, ROOT('logs')
					) as xmlDetails
				FROM ##tmpLogData as tmp
				GROUP BY tmp.orgID, tmp.orgcode, tmp.organizationName
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="Avg">
						ORDER BY avgMS desc
					</cfcase>
					<cfcase value="Max">
						ORDER BY maxMS desc
					</cfcase>
					<cfcase value="Min">
						ORDER BY minMS desc
					</cfcase>
					<cfcase value="Std">
						ORDER BY standardDeviation desc
					</cfcase>
					<cfcase value="OrgName">
						ORDER BY tmp.orgcode desc
					</cfcase>
					<cfcase value="Sum">
						ORDER BY sumMS desc
					</cfcase>
					<cfdefaultcase>
						ORDER BY avgMS desc
					</cfdefaultcase>
				</cfswitch>;

				IF OBJECT_ID('tempdb..##tmpLogData') IS NOT NULL 
					DROP TABLE ##tmpLogData;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = arrayNew(1)>
			<cfloop query="local.qryMemGrpHistoryLogs">
				<cfset local.arrLogs = XMLParse(local.qryMemGrpHistoryLogs.xmlDetails).xmlRoot.xmlChildren.map(function(item){
					return arguments.item.XMLAttributes;
 					})>

				<cfset local.thisElement = {}>
				<cfset local.thisElement['orgcode'] = local.qryMemGrpHistoryLogs.orgcode>
				<cfset local.thisElement['orgName'] = local.qryMemGrpHistoryLogs.organizationName>
				<cfset local.thisElement['avgMS'] = local.qryMemGrpHistoryLogs.avgMS>
				<cfset local.thisElement['minMS'] = local.qryMemGrpHistoryLogs.minMS>
				<cfset local.thisElement['maxMS'] = local.qryMemGrpHistoryLogs.maxMS>
				<cfset local.thisElement['standardDeviation'] = round(val(local.qryMemGrpHistoryLogs.standardDeviation))>
				<cfset local.thisElement['numRuns'] = local.qryMemGrpHistoryLogs.numRuns>
				<cfset local.thisElement['sumMS'] = local.qryMemGrpHistoryLogs.sumMS>
				<cfset local.thisElement['arrlogs'] = local.arrLogs>
				<cfset arrayAppend(local.strReturn['data'], local.thisElement)>
			</cfloop>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="listSystemOperations" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objDesktop = createObject("component","desktop")>

		<cfset local.qrySites = local.objDesktop.getSites()>
		<cfset local.qryOrgs = local.objDesktop.getOrganizations()>

		<cfset local.dashboardRootURL = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=dashboard&mca_jsonfunc=">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_systemOperations.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addSessionToKillList" access="public" output="false" returntype="string">
		<cfargument name="cfid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfif len(arguments.cfid)>
			<cfset application.objPlatform.addSessionToKillList(cfid=arguments.cfid, killReasonCode='Manual')>
			<cfset local.strReturn['success'] = true>
		<cfelse>
			<cfset local.strReturn['success'] = false>
		</cfif>
		
		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="listPermissionRoles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.objDesktop = createObject("component","desktop")>
		<cfset local.listPermissionRolesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getPermissionRoles&mode=stream'>
		<cfset local.permissionsExportLink = buildCurrentLink(arguments.event,"exportPermissionRoles") & "&mode=stream">

		<cfset local.qryResourceTypes = local.objDesktop.getResourceTypes()>
		<cfset local.qryRoles = local.objDesktop.getRoles()>
		<cfset local.qryToolTypes = local.objDesktop.getToolTypes()>
		<cfset local.qryApplicationTypes = local.objDesktop.getApplicationTypes()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_permissionRoles.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportPermissionRoles" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "MCPlatformPermissions.csv">
		<cfset local.listPermissionsLink = buildCurrentLink(arguments.event,"listPermissionRoles")>

		<cfset createObject("component","desktop").getPermissionsFromFilters(event=arguments.event, operationMode="export", 
			reportFileName="#local.strFolder.folderPathUNC#\#local.reportFileName#")>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#",
			displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#local.listPermissionsLink#" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="listDashboardObjects" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.qryDashboardObjectTypes = createObject("component","model.admin.dashboards.dashboard").getDashboardObjectTypes(siteID=local.siteCode eq "MC" ? 0 : local.siteID)>

		<cfquery name="local.qrySitesWithDashboards" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct s.siteID, s.siteCode, s.siteName
			from dbo.sites as s
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			inner join dbo.rpt_dashboards as d on d.siteID = s.siteID
			order by s.siteCode;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfstoredproc procedure="rpt_getDashboardStatsSummary" datasource="#application.dsn.membercentral.dsn#">
			<cfif local.siteCode eq "MC">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" null="yes">
			<cfelse>
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			</cfif>
			<cfprocresult name="local.qryDashboardStatsSummary">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_dashboardCharts.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="filterDashboardObjectsResult" access="public" output="false" returntype="string">
		<cfargument name="thisSiteCode" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="objectTypeIDList" type="string" required="true">
		<cfargument name="orderBy" type="string" required="true">
		<cfargument name="count" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":false, "arrdata":[] }>

		<cfif arguments.thisSiteCode neq "MC">
			<cfset arguments.siteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode=arguments.thisSiteCode)>
		</cfif>

		<cftry>
			<cfif NOT listFindNoCase("site,object,date,max",arguments.orderBy)>
				<cfset arguments.orderBy = "max"> 
			</cfif>

			<cfquery name="local.qryDashboardObjects" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @count int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				IF @count not in (25,50,100)
					SET @count = 25;

				select TOP (@count) s.sitecode, dot.objectTypeCode, dot.objectTypeTitle, max(doc.lastUpdated) as lastRun, max(doc.timeMS) as maxMS
				from platformStatsMC.dbo.rpt_dashboardObjectsCache as doc
				inner join dbo.rpt_dashboardObjects as do on do.objectID = doc.objectID
				inner join dbo.rpt_dashboards as d on d.dashboardID = do.dashboardID 
					and d.maskData = 0
				inner join dbo.rpt_dashboardObjectTypes as dot on dot.objectTypeID = do.objectTypeID
				inner join dbo.sites as s on s.siteID = d.siteID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				where 1 = 1
				<cfif arguments.siteID gt 0>
					and s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfif>
				<cfif listLen(arguments.objectTypeIDList)>
					and dot.objectTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.objectTypeIDList#">)
				</cfif>
				group by s.sitecode, dot.objectTypeCode, dot.objectTypeTitle
				<cfswitch expression="#arguments.orderBy#">
					<cfcase value="site">
						order by s.sitecode asc, maxMS desc, dot.objectTypeCode asc
					</cfcase>
					<cfcase value="object">
						order by dot.objectTypeCode asc, maxMS desc, s.sitecode asc
					</cfcase>
					<cfcase value="date">
						order by lastRun desc, maxMS desc, dot.objectTypeCode asc, s.sitecode asc
					</cfcase>
					<cfcase value="max">
						order by maxMS desc, dot.objectTypeCode asc, s.sitecode asc
					</cfcase>
				</cfswitch>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfloop query="local.qryDashboardObjects">
				<cfset local.strReturn['arrdata'].append(
					{ 
						"objecttypetitle":local.qryDashboardObjects.objectTypeTitle,
						"objecttypecode":local.qryDashboardObjects.objectTypeCode,
						"sitecode":local.qryDashboardObjects.sitecode,
						"lastrun":dateTimeformat(local.qryDashboardObjects.lastRun,"mmm d - H:nn:ss tt"),
						"maxms":local.qryDashboardObjects.maxMS
					}
				)>
			</cfloop>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="filterDashboardStatsResult" access="public" output="false" returntype="string">
		<cfargument name="thisSiteCode" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="statsMode" type="string" required="true">
		<cfargument name="count" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = 
					{ "success":false, 
						"strdata": {
							"statsmode":"",
							"arrdata":[] 
						} 
				}>
		
		<cfif arguments.thisSiteCode neq "MC">
			<cfset arguments.siteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode=arguments.thisSiteCode)>
			<cfset arguments.statsMode = "allBySite">
			<cfset arguments.count = "all">
		</cfif>

		<cftry>
			<cfif NOT listFindNoCase("groupBySite,allBySite,allByLastViewed",arguments.statsMode)>
				<cfset arguments.statsMode = "groupBySite"> 
			</cfif>

			<cfset local.strReturn["strdata"]["statsmode"] = lCase(arguments.statsMode)>

			<cfquery name="local.qrySiteDashboardStats" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif listFindNoCase("allBySite,allByLastViewed",arguments.statsMode) and arguments.count neq 'all'>
					DECLARE @count int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
					IF @count not in (25,50,100)
						SET @count = 25;
				</cfif>

				select 
					<cfif listFindNoCase("allBySite,allByLastViewed",arguments.statsMode) and arguments.count neq 'all'>
						TOP (@count)
					</cfif>
					s.sitecode, d.dashboardName, d.maskData, count(distinct do.objectID) as chartscount, max(dov.dateViewed) as lastviewed,
					m.firstName + ' ' + m.lastName + ' (' + m.memberNumber + ')' as createdByMember, d.dateCreated
				from dbo.rpt_dashboards as d
				inner join dbo.rpt_dashboardObjects as do on do.dashboardID = d.dashboardID
				inner join dbo.sites as s on s.siteID = d.siteID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.ams_members as m on m.memberID = d.createdByMemberID
				left outer join platformStatsMC.dbo.rpt_dashboardViews as dov on dov.dashboardID = d.dashboardID
				where 1 = 1
				<cfif arguments.siteID gt 0>
					and s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfif>
				group by s.sitecode, d.dashboardName, d.maskData, m.firstName, m.lastName, m.memberNumber, d.dateCreated
				<cfswitch expression="#arguments.statsMode#">
					<cfcase value="allBySite">
						order by s.sitecode, d.dashboardName, lastviewed desc, d.dateCreated
					</cfcase>
					<cfcase value="allByLastViewed">
						order by lastviewed desc, s.sitecode, d.dashboardName, d.dateCreated
					</cfcase>
					<cfdefaultcase>
						order by s.sitecode
					</cfdefaultcase>
				</cfswitch>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfswitch expression="#arguments.statsMode#">
				<cfcase value="allBySite,allByLastViewed">
					<cfloop query="local.qrySiteDashboardStats">
						<cfset local.strReturn["strdata"]['arrdata'].append(
								{ 
									"sitecode":local.qrySiteDashboardStats.sitecode, 
									"dashboardname":local.qrySiteDashboardStats.dashboardName, 
									"maskdata":local.qrySiteDashboardStats.maskdata, 
									"chartscount":local.qrySiteDashboardStats.chartscount,
									"lastviewed":dateTimeformat(local.qrySiteDashboardStats.lastviewed,"mmm d - H:nn:ss tt"),
								}
							)>
					</cfloop>
				</cfcase>
				<cfcase value="groupBySite">
					<cfquery name="local.qryGroupBySiteDashboardStats" datasource="#application.dsn.memberCentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @count int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
						IF @count not in (25,50,100)
							SET @count = 25;

						select TOP (@count) s.sitecode, count(distinct d.dashboardID) as dashboardscount, count(distinct do.objectID) as chartscount,
							max(dov.dateViewed) as lastviewed
						from dbo.rpt_dashboards as d
						inner join dbo.rpt_dashboardObjects as do on do.dashboardID = d.dashboardID
						inner join dbo.sites as s on s.siteID = d.siteID
						inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
						left outer join platformStatsMC.dbo.rpt_dashboardViews as dov on dov.dashboardID = d.dashboardID
						where 1 = 1
						<cfif arguments.siteID gt 0>
							and s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
						</cfif>
						group by s.sitecode
						order by s.sitecode;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfloop query="local.qryGroupBySiteDashboardStats">
						<cfset var arrThisSiteDashboards = []>
						<cfquery name="arrThisSiteDashboards" dbtype="query" returntype="array">
							select sitecode, dashboardname, maskdata, chartscount, lastviewed, createdbymember, datecreated
							from [local].qrySiteDashboardStats
							where sitecode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGroupBySiteDashboardStats.sitecode#">
							order by dashboardName
						</cfquery>

						<cfset arrThisSiteDashboards.map(
								function(thisRow) {
									arguments.thisRow.lastviewed = dateTimeformat(arguments.thisRow.lastviewed,"mmm d - H:nn tt");
									arguments.thisRow.datecreated = dateTimeformat(arguments.thisRow.datecreated,"mmm d - H:nn tt");
								})>

						<cfset local.strReturn["strdata"]['arrdata'].append(
							{ 
								"sitecode":local.qryGroupBySiteDashboardStats.sitecode, 
								"dashboardscount":local.qryGroupBySiteDashboardStats.dashboardscount, 
								"chartscount":local.qryGroupBySiteDashboardStats.chartscount,
								"lastviewed":dateTimeformat(local.qryGroupBySiteDashboardStats.lastviewed,"mmm d - H:nn tt"),
								"arrsitedashboards":arrThisSiteDashboards
							}
						)>
					</cfloop>
				</cfcase>
			</cfswitch>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="listSiteMapStats" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.qrySites = createObject("component","desktop").getSites()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_siteMaps.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="markTaskAsDone" access="public" output="false" returntype="struct">
		<cfargument name="taskId" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateScheduledTask">
				UPDATE dbo.scheduledTasks
				SET isRunning = 0
				WHERE taskid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskId#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="listMemberLogins" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset local.listMemberLoginsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=get2FALogs&gridMode=mcPlatformWideGrid&mode=stream">
		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
		<cfset local.fDateFrom = dateFormat(dateAdd("m",-1,now()), "m/d/yyyy")>
		<cfset local.fDateTo = dateFormat(now(), "m/d/yyyy")>
		<cfset local.qrySites = createObject("component","desktop").getSites()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberLogins.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listReportLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.sitecode')>
		<cfset arguments.event.paramValue('fdaterunfrom',DateFormat(DateAdd('d',-7,Now()), 'mm/dd/yyyy'))>
		<cfset arguments.event.paramValue('fdaterunto',dateformat(now(),"m/d/yyyy"))>
		<cfset local.listReportLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getReportLogs&mode=stream">
		<cfset local.qrySites = createObject("component","desktop").getSites()>

		<cfquery name="local.qryReportTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct r.toolTypeID, tt.toolType, tt.toolDesc as typeName
			from dbo.rpt_SavedReports as r
			inner join dbo.admin_toolTypes as tt on tt.toolTypeID = r.toolTypeID 
			inner join dbo.sites as s on s.siteID = r.siteID
			inner join dbo.cms_siteResources as sr on sr.siteID = s.siteID
				and sr.siteResourceID = s.siteResourceID
				and sr.siteResourceStatusID = 1
			<cfif local.siteCode neq "MC">
				where r.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">
			</cfif>
			order by tt.toolDesc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reportLogs.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listARCacheLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>		
		<cfset local.listARCacheLogsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getARCacheLogs&mode=stream'>
		<cfset local.fDateStartedFrom = dateFormat(dateAdd("d",-3,now()), "m/d/yyyy")>
		<cfset local.fDateStartedTo = dateFormat(now(), "m/d/yyyy")>
		<cfset local.orgCode = arguments.event.getValue('mc_siteInfo.orgCode')>

		<cfif local.orgCode EQ "MC">
			<cfquery name="local.qryOrgs" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct o.orgID, o.orgCode, oi.organizationName as orgName
				from dbo.organizations o
				inner join dbo.sites s on s.orgID = o.orgID
				inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
				order by o.orgCode, oi.organizationName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_arCacheLogs.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listMemGrpHistoryLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>	
		<cfset local.fDateStartedFrom = dateFormat(dateAdd("d",-3,now()), "m/d/yyyy")>
		<cfset local.fDateStartedTo = dateFormat(now(), "m/d/yyyy")>
		<cfset local.orgCode = arguments.event.getValue('mc_siteInfo.orgCode')>

		<cfif local.orgCode EQ "MC">
			<cfquery name="local.qryOrgs" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct o.orgID, o.orgCode, oi.organizationName as orgName
				from dbo.organizations o
				inner join dbo.sites s on s.orgID = o.orgID
				inner join dbo.cms_siteResources sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
				order by o.orgCode, oi.organizationName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memGrpHistoryLogs.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listAuthorizeProfiles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.listAllPaymentProfilesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=desktopJSON&meth=getAuthorizePaymentProfiles&mode=stream">
		<cfset local.qrySites = createObject("component","desktop").getSites()>
		<cfset local.viewSavingsCalculator = buildLinkToTool(toolType='MerchantProfilesAdmin',mca_ta='viewSavingsCalculator') & "&mode=direct">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listAuthorizeProfiles.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	
</cfcomponent>