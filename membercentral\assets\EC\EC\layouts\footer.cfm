<cfoutput>
    <cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
   
    <footer class="footer">
        <div class="container" id="footerMenuHolder">
            <cfif structKeyExists(local.strMenus,"footerNav")>
                #local.strMenus.footerNav.menuHTML.rawcontent#
                <div style="display:none;" id="FooterLinks">
                    <cfif application.objCMS.getZoneItemCount(zone='E',event=event)>    
                        <cfloop array="#event.getValue("mc_pageDefinition").pageZones['E']#" index="local.zoneEFLContent"> 
                            <cfif LCase(trim(local.zoneEFLContent.contentAttributes.contentTitle)) EQ "footer links">       
                                #REReplace(REReplace(local.zoneEFLContent.data,"<p>",""),"</p>","")#
                            </cfif>    
                        </cfloop> 
                    </cfif>                    
                </div>
            </cfif>
            <div class="footerBtm">
                <div class="row-fluid">
                    <div class="span9 contactDetails">
                       <cfif application.objCMS.getZoneItemCount(zone='E',event=event)>    
                            <cfloop array="#event.getValue("mc_pageDefinition").pageZones['E']#" index="local.zoneECIContent"> 
                                <cfif LCase(trim(local.zoneECIContent.contentAttributes.contentTitle)) EQ "contact info">       
                                    #local.zoneECIContent.data#
                                </cfif>    
                            </cfloop> 
                        </cfif>  
                    </div>
                    <div class="span3">
                        <div id="socialHolder" class="social">
                            <cfif application.objCMS.getZoneItemCount(zone='E',event=event)>    
                                <cfloop array="#event.getValue("mc_pageDefinition").pageZones['E']#" index="local.zoneSASContent"> 
                                    <cfif LCase(trim(local.zoneSASContent.contentAttributes.contentTitle)) EQ "social and site by">       
                                        #local.zoneSASContent.data#
                                    </cfif>    
                                </cfloop> 
                            </cfif>
                        </div>
						<div id="siteBy" style="margin-top:20px;text-align:right">
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

</cfoutput>