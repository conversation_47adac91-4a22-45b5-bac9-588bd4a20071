@charset "utf-8";
@media only screen and (max-width:1399px) {
.banner-content p,.bannerItem > div:first-child > div:last-child p { font-size: 20px; line-height: 30px; }
}
@media only screen and (max-width:1199px) {
.header .navbar .container { width: 940px; }
.logo { max-width: 500px; }
.header-top-rt { padding-left: 20px; }
.banner-content p,.bannerItem > div:first-child > div:last-child p { font-size: 18px; line-height: 28px; }
.header .navbar .nav li a, .header .navbar .nav li .dropdown-menu > li > a { font-size: 15px; }
.sep { width: 2%; }
.cols.tweet { width: 33%; }
.brands ul { padding: 0 10px; }
.footerCols h4,#footerMenuHolder > ul h4 { font-size: 12px; }
.search-box input,.header-top-rt > div:nth-child(2) input,#footerMenuHolder > ul > li:last-child > div input { width: 185px; }
}
@media only screen and (min-width:979px) {
.header .dropdown-menu, .header .dropdown-submenu .dropdown-menu { display: block!important; opacity: 0; transition: all 0.3s ease-in-out 0s; visibility: hidden; }
.header .dropdown:hover > .dropdown-menu, .header .dropdown-submenu:hover > .dropdown-menu { opacity: 1; visibility: visible; }
.header .dropdown-menu { z-index: 999; }
.header .dropdown-submenu > .dropdown-menu { z-index: 9; }
.search-mob-box, .search-box-mobile,.header-top-rt > div:last-child { display: none; }
.header .nav-collapse .nav { display: table; width: 100%; }
.navbar .nav > li { float: none; padding: 0 12px; display: table-cell; text-align: left; vertical-align: middle; position: relative; }
}
@media only screen and (max-width:979px) {
.header .navbar .container, .container { width: 90%; position: relative; }
header { margin: 0 auto; }
.header .navbar .nav li a { margin: 0; border: none; color: #fff; }
.footerCols > li ul li, #footerMenuHolder > ul > li ul li,.nav .dropdown li { border-top: 1px solid rgba(255,255,255,0.3); }
.logo { margin: 0 auto; text-align: center; }
.navMain { float: none; height: 60px; padding: 0; background: #515151; border-top: 1px solid #949494; }
.header .navbar-inner { background: #fff; }
.header .navbar .navIcon { display: block; position: absolute; left: 12px; z-index: 99; bottom: 15px; z-index: 99; }
.header .navbar .btn-navbar:hover span { color: #cccccc; }
.header.fixed-header .navbar .btn-navbar { top: 9px; }
.search { right: 90px; top: 19px; }
.header .navbar-inner { position: relative; }
.header .nav-collapse li { width: 100%; display: block; }
.header .nav-collapse { padding: 0; float: none; width: 100%; top: 59px; background: #656565; z-index: 99; border: none; border-top: 2px solid #fff; padding: 0 15px; }
.header .nav-collapse li a {background-color:rgba(0, 0, 0, 0.03)padding:10px 15px; border-bottom: 1px solid #2a2a2a; font-size: 13px; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; }
.header.fixed-header .nav-collapse { padding: 0; }
.navbar .btn-navbar { background: rgba(0, 0, 0, 0) none repeat scroll 0 0; border: medium none; border-radius: 4px; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; line-height: 1.42857; margin: 0; padding: 10px 12px; }
.header-top-rt { margin-top: 0; }
.header .navbar-static .navbar-inner { padding-left: 0; padding-right: 0; }
.header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled] { background: none; color: #ccc; }
.header .dropdown-menu>li>a:hover, .header .dropdown-menu>li>a:focus, .header .dropdown-submenu:hover>a, .header .dropdown-submenu:focus>a { background: rgba(0, 0, 0, 0.03); color: #dd3333; }
.header .navbar .nav li .dropdown-menu > li > a { padding: 10px 0; margin: 0; }
.header .navbar .nav li a { text-align: left; }
.header ul.dropdown-menu { margin: 0; }
.header .dropdown-menu { width: 100%; min-width: inherit; }
.search-box, .header-top-rt > div:nth-child(2),#footerMenuHolder > ul > li:last-child > div { display: none; }
.navMain .search-box { display: block; float: none; padding: 24px 15px; }
.navMain .search-box form { margin-bottom: 0; }
.search-mob-box input { width: 100%; height: 50px; background: #656565; border: 1px solid rgba(0, 0, 0, 0.2); color: #fff; font-size: 22px; }
.buttonsDiv,.header-top-rt > div:first-child { position: absolute; text-align: center; width: 100%; bottom: 16.5px; left: 50%; -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); -o-transform: translateX(-50%); -webkit-transform: translateX(-50%); transform: translateX(-50%); }
.buttonsDiv li,.header-top-rt > div:first-child li { display: inline-block; margin-bottom: 0; margin-right: 10px; }
.buttonsDiv li:last-child,.header-top-rt > div:first-child li:last-child { margin-right: 0; }
.buttonsDiv li a,.header-top-rt > div:first-child li a { padding: 0; min-width: inherit; background: transparent; }
.logo { max-width: inherit; display: block; }
.logo img{width: 100%!important;}
.search-box-mobile,.header-top-rt > div:last-child { position: absolute; font-size: 16px; right: 15px; bottom: 19px; color: #fff; cursor: pointer; }
.header .container { padding: 0; }
.navIcon .btn.btn-navbar { padding: 0; }
.search-box button.btn,.header-top-rt > div:nth-child(2) button.btn,#footerMenuHolder > ul > li:last-child > div button.btn { background: #3465a8; color: #fff; width: 51px; font-size: 20px; }
.nav-collapse .nav > li, .footerCols > li { border-top: 1px solid #d8d8d8; padding: 7px 0; }
.header .navbar .container { position: static; }
.header .navbar { position: relative; }
.nav-collapse .dropdown-menu { display: block; }
.footerCols *,#footerMenuHolder > ul * { font-size: 15px; }
.footerCols h4, #footerMenuHolder > ul h4,.header .navbar .nav li .dropdown-menu > li > a, .header .navbar .nav li a { font-size: 22px; line-height: normal; }
.header .navMain .container { width: 100%; }
.banner-content p,.bannerItem > div:first-child > div:last-child p { font-size: 15px; line-height: 22px; margin-bottom: 10px; }
.banner-content,.bannerItem > div:first-child > div:last-child { padding-right: 30px; }
.homeCols .cols { display: block; width: 100%; border: none; }
.sep { width: 0; display: none; }
.footerCols > li,#footerMenuHolder > ul > li { display: block; padding-right: 0; }
.footerCols,#footerMenuHolder > ul { display: block; }
.footerCols > li ul,#footerMenuHolder > ul > li ul { padding: 0px 8px 3px; }
.footerCols > li ul li,#footerMenuHolder > ul > li ul li { margin-bottom: 0; padding: 7px 0; }
.footerCols .search-box,#footerMenuHolder > ul > li:last-child > div { display: block; }
.footerCols .search-box input,#footerMenuHolder > ul > li:last-child > div input { width: 100%; height: 40px; }
.footerCols form,#footerMenuHolder > ul form { margin: 0; }
.footerCols,#footerMenuHolder > ul { border-bottom: none; }
.events .homeCols-content { padding: 15px 0; }
#s_key_all { width: 100%!important;}
}
@media only screen and (max-width:767px) {
.header .navbar .container { width: auto; }
.desktop-banner,.bannerItem > div:first-child > div:first-child { display: none; }
.mobile-banner,.bannerItem > div:first-child > :nth-child(2) { display: block; }
.banner-content,.bannerItem > div:first-child > div:last-child { display: block; width: 100%; max-width: inherit; padding: 16px; }
.mobile-banner img,.bannerItem > div:first-child > :nth-child(2) img { width: 100%; }
.banner-content p,.bannerItem > div:first-child > div:last-child p { font-size: 18px; line-height: 26px; }
.span4.social { position: absolute; bottom: 13px; left: 0; width: 100%; text-align: left; }
.footer { padding: 15px 0 0; }
.footerEnd { margin-top: 20px; }
.footerBtm { position: relative; }
.header .navbar .container, .container { width: auto; }
.inner-banner-content,#titleBanner > div:first-Child > div > div { height: 120px; width: 280px; }
.inner-banner-img,#titleBanner > div:first-Child { background-size: auto 120px; }
.inner-banner-content h1,#titleBanner > div:first-Child > div > div h1 { font-size: 30px; }
p { font-size: 20px; }
.owl-theme .owl-nav.disabled + .owl-dots{
    left: auto!important;
}
#s_key_all {
    width: 100%!important;
}
}
 @media only screen and (max-width:688px) {
.mission-img { background-image: url(../img/hdr_bc_mob.jpg); background-size: auto 120px; background-position: right top; }
}

@media screen and (max-width:479px) { .zone-m-slider .item img{ max-width: 340px !important; max-height: 136px !important; }}
@media screen and (min-width:480px) and (max-width:767px) { .zone-m-slider .item img{ max-width: 297px !important; max-height: 119px !important; }}
@media screen and (min-width:768px) { .zone-m-slider .item img{ max-width: 250px !important; max-height: 100px !important; }}
