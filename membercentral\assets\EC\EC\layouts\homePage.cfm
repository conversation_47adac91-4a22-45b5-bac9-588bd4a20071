<cfoutput>
    <!doctype html>
    <html>

        <head>
            <cfinclude template="head.cfm">
        </head>

        <body>
            <div class="wrapper outer-width in-wrapper">

                <!--Header Start-->
                    <cfinclude template="header.cfm">     
                <!--Header End-->
                <!--Banner Start-->
                <cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>   
                    <cfif lcase(trim(event.getValue("mc_pageDefinition").pageZones['Main'][1].view)) EQ 'echo'> 
                        <div class="owl-carousel owl-theme owl-slider">    
                            <cfloop index="local.i" from="1" to="#arrayLen(event.getValue("mc_pageDefinition").pageZones['Main'])#">   
                                <cfif len(trim(event.getValue("mc_pageDefinition").pageZones['Main'][local.i].data))>
                                        #event.getValue("mc_pageDefinition").pageZones['Main'][local.i].data#	
                                </cfif>
                            </cfloop>      
                        </div> 
                    <cfelse>
                        <section class="banner-section">					
                            #application.objCMS.renderZone(zone='Main',event=event)#
                        </section>
                    </cfif>
                </cfif> 
                <!--Banner End-->
                <!--COntent Start-->
                <div class="content">
                    <div class="container">
                        <div class="row-fluid homeCols">
                            <div class="span4 cols events">
                                <h2>
                                    <cfif application.objCMS.getZoneItemCount(zone='H',event=event)>    
                                        <cfloop array="#event.getValue("mc_pageDefinition").pageZones['H']#" index="local.zoneHTContent"> 
                                            <cfif LCase(trim(local.zoneHTContent.contentAttributes.contentTitle)) EQ "title">       
                                                #REReplace(REReplace(local.zoneHTContent.data,"<p>",""),"</p>","")#
                                            </cfif>    
                                        </cfloop> 
                                    </cfif> 
                                </h2>
                                <div class="homeCols-content">
                                    <cfif application.objCMS.getZoneItemCount(zone='H',event=event)>    
                                        <cfloop array="#event.getValue("mc_pageDefinition").pageZones['H']#" index="local.zoneHIContent"> 
                                            <cfif LCase(trim(local.zoneHIContent.contentAttributes.contentTitle)) EQ "information">       
                                                #local.zoneHIContent.data#
                                            </cfif>    
                                        </cfloop> 
                                    </cfif>                               
                                </div>
                            </div>
                            <div class="sep"></div>
                            <div class="span4 cols tweet">
                                <cfif application.objCMS.getZoneItemCount(zone='I',event=event)>    
                                    <cfloop array="#event.getValue("mc_pageDefinition").pageZones['I']#" index="local.zoneITContent"> 
                                        <cfif LCase(trim(local.zoneITContent.contentAttributes.contentTitle)) EQ "title">       
                                            #REReplace(REReplace(local.zoneITContent.data,"<p>",""),"</p>","")#
                                        </cfif>    
                                    </cfloop> 
                                </cfif> 
                                <cfif application.objCMS.getZoneItemCount(zone='I',event=event)>    
                                    <cfloop array="#event.getValue("mc_pageDefinition").pageZones['I']#" index="local.zoneIIContent"> 
                                        <cfif LCase(trim(local.zoneIIContent.contentAttributes.contentTitle)) EQ "information">       
                                            #REReplace(REReplace(local.zoneIIContent.data,"<p>",""),"</p>","")#
                                        </cfif>    
                                    </cfloop> 
                                </cfif>                                 
                            </div>
                            <div class="sep"></div>
                            <div class="span4 cols brands">
                                <cfif application.objCMS.getZoneItemCount(zone='J',event=event)>    
                                    <cfloop array="#event.getValue("mc_pageDefinition").pageZones['J']#" index="local.zoneJContent"> 
                                        <cfif LCase(trim(local.zoneJContent.contentAttributes.contentTitle)) EQ "benefits">       
                                            #REReplace(REReplace(local.zoneJContent.data,"<p>",""),"</p>","")#
                                        </cfif>    
                                    </cfloop> 
                                </cfif>
								<div class="zoneKContent" style="display:none">
									<cfif application.objCMS.getZoneItemCount(zone='K',event=event)> 
										<cfloop array="#event.getValue('mc_pageDefinition').pageZones['K']#" index="local.zoneKContent"> 
											<cfif Trim(local.zoneKContent.data) neq ''>
												 #REReplace(REReplace(local.zoneKContent.data,"<p>",""),"</p>","")# 
											</cfif>
											</cfloop> 
									</cfif>
								</div>	
								<div class="zoneLContent" style="display:none">
									<cfif application.objCMS.getZoneItemCount(zone='L',event=event)> 
										<cfloop array="#event.getValue("mc_pageDefinition").pageZones['L']#" index="local.zoneLContent"> 
											<cfif Trim(local.zoneLContent.data) neq ''>
												 #REReplace(REReplace(local.zoneLContent.data,"<p>",""),"</p>","")# 
											</cfif>
											</cfloop> 
									</cfif>
								</div>
																	
                            </div>
						</div>
                    </div>					
                </div>
                <!--Content End-->
                <!--Footer Start-->
                    <cfinclude template="footer.cfm">          
                <!--Footer End-->
            </div>

            <cfinclude template="foot.cfm">
            <cfinclude template="toolBar.cfm">

        </body>

    </html>
</cfoutput>