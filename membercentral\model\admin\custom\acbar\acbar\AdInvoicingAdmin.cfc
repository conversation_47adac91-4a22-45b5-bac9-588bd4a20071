<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.methodToRun = this[arguments.event.getValue('mca_ta')]>
		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>

	<cffunction name="manageAdInvoicing" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.listInsertionOrdersLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=ACBARadInvJSON&meth=getInsertionOrders&mode=stream">
		<cfset local.addInsertionOrderLink = buildCurrentLink(arguments.event,"addInsertionOrder") & "&mode=direct">
		<cfset local.listRateCardsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=ACBARadInvJSON&meth=getRateCards&mode=stream">
		<cfset local.editRateCardLink = buildCurrentLink(arguments.event,"editRateCard") & "&mode=direct">
		<cfset local.editRateCardConfigLink = buildCurrentLink(arguments.event,"editRateCardConfig") & "&mode=direct">
		<cfset local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & "&disableAddMemberAction=1">
		<cfset local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>
		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
		<cfset local.qryRateCards = getRateCards()>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_adInvoicing.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addInsertionOrder" access="public" output="false" returntype="struct" hint="add/edit rate card">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryRateCards = getRateCards()>
		<cfset local.saveInsertionOrderLink = buildCurrentLink(arguments.event,"saveInsertionOrder") & "&mode=stream">
		<cfset local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & "&addMemNewTab=1">

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_adInvoicing_insertionOrder.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveInsertionOrder" access="public" output="false" returntype="struct" >
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.arrAdRuns = []>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
			<cfif left(local.thisField,4) eq "run_" and right(local.thisField,5) eq "_date" and arguments.event.getTrimValue(local.thisField)>
				<cfset local.thisID = GetToken(local.thisField,2,'_')>
				<cfset arrayAppend(local.arrAdRuns, { 
					"rundate":arguments.event.getTrimValue(local.thisField), 
					"price":val(ReReplace(arguments.event.getValue('run_#local.thisID#_price',0),'[^0-9\.]','','ALL'))
				})>
			</cfif>
		</cfloop>

		<cfset arraySort(local.arrAdRuns, function(run1, run2) { return dateCompare(run1.rundate, run2.rundate); })>

		<cftry>
			<cfxml variable="local.runDatesXML">
				<cfoutput>
					<runs>
						<cfif arrayLen(local.arrAdRuns)>
							<cfloop array="#local.arrAdRuns#" index="local.thisAdRun">
								<run date="#local.thisAdRun.rundate#" price="#local.thisAdRun.price#" />
							</cfloop>
						</cfif>
					</runs>
				</cfoutput>
			</cfxml>

			<!--- remove the <xml> tag, specifically the encoding. --->
			<cfset local.runDatesXML = replaceNoCase(toString(local.runDatesXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfstoredproc datasource="#application.dsn.customApps.dsn#" procedure="acbar_createInsertionOrder">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('advertiserMemberID',0)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rateCardConfigID',0)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.runDatesXML#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('PLJNumber','')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('referenceInfo','')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcUser.statsSessionID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.orderID">
			</cfstoredproc>

			<cfset local.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfset local.data = serializeJSON({ "success":local.success })>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRateCardConfigsByRateCardID" access="public" returnType="struct">
		<cfargument name="rateCardID" type="numeric" required="true">
		
		<cfset var arrConfigs = []>

		<cfquery name="arrConfigs" datasource="#application.dsn.customApps.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT rateCardConfigID as ratecardconfigid, configName as configname,
				runs, pricePerRun as priceperrun, allowRunOverride as allowrunoverride,
				allowPriceOverride as allowpriceoverride
			FROM dbo.ACBAR_AdInvoicing_rateCardConfigs
			WHERE rateCardID = <cfqueryparam value="#arguments.rateCardID#" cfsqltype="cf_sql_integer" />;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "arrconfigs":arrConfigs }>
	</cffunction>

	<cffunction name="editRateCard" access="public" output="false" returntype="struct" hint="add/edit rate card">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.rateCardID = val(arguments.event.getValue('rateCardID',0))>
		<cfset local.qryRateCard = getRateCard(rateCardID=local.rateCardID)>
		<cfset local.saveRateCardLink = buildCurrentLink(arguments.event,"saveRateCard") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_adInvoicing_rateCard.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="saveRateCard" access="public" output="false" returntype="struct" >
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.rateCardID = val(arguments.event.getValue('rateCardID',0))>
		<cfset local.rateCardName = arguments.event.getValue('rateCardName','')>

		<cftry>
			<cfquery name="local.qrySaveRateCard" datasource="#application.dsn.customApps.dsn#">
				<cfif local.rateCardID gt 0>
					UPDATE dbo.ACBAR_AdInvoicing_rateCards
					SET rateCardName = <cfqueryparam value="#local.rateCardName#" cfsqltype="cf_sql_varchar" />
					WHERE rateCardID = <cfqueryparam value="#local.rateCardID#" cfsqltype="cf_sql_integer" />
				<cfelse>
					INSERT INTO dbo.ACBAR_AdInvoicing_rateCards(rateCardName, createdDate)
					VALUES(<cfqueryparam value="#local.rateCardName#" cfsqltype="cf_sql_varchar" />, GETDATE())
				</cfif>
			</cfquery>
			
			<cfset local.success = true />
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.success = false />
		</cfcatch>
		</cftry>

		<cfset local.data = serializeJSON({ "success":local.success })>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRateCard" access="private" output="false" returntype="query">
		<cfargument name="rateCardID" type="numeric"  required="true" />

		<cfset var local = structNew() />

		<cfquery name="local.qryRateCard" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT rateCardName
			FROM dbo.ACBAR_AdInvoicing_rateCards
			WHERE rateCardID = <cfqueryparam value="#arguments.rateCardID#" cfsqltype="cf_sql_integer" />
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryRateCard />
	</cffunction>

	<cffunction name="getRateCards" access="private" output="false" returntype="query">

		<cfset var local = structNew() />

		<cfquery name="local.qryRateCards" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT rateCardID, rateCardName
			FROM dbo.ACBAR_AdInvoicing_rateCards
			ORDER BY rateCardName;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryRateCards />
	</cffunction>

	<cffunction  name="deleteRateCard" access="public" output="false" returntype="struct">
		<cfargument name="rateCardID" type="numeric"  required="true" />

		<cfset var local = structNew()>
		<cfset local.result = StructNew()>

		<cftry>
			<cfquery name="local.qryDelete" datasource="#application.dsn.customApps.dsn#">
				SET NOCOUNT ON;

				DECLARE @rateCardID int = <cfqueryparam cfsqltype="cf_sql_integer"  value="#arguments.rateCardID#" />;
				
				IF NOT EXISTS (
					SELECT 1
					FROM dbo.ACBAR_AdInvoicing_insertionOrders AS o
					INNER JOIN dbo.ACBAR_AdInvoicing_rateCardConfigs AS cc ON cc.rateCardConfigID = o.rateCardConfigID
					WHERE cc.rateCardID = @rateCardID
				) BEGIN
					DELETE
					FROM dbo.ACBAR_AdInvoicing_rateCardConfigs
					WHERE rateCardID = @rateCardID;

					DELETE
					FROM dbo.ACBAR_AdInvoicing_rateCards
					WHERE rateCardID = @rateCardID;
				END
			</cfquery>
			
			<cfset local.result.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.result.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="editRateCardConfig" access="public" output="false" returntype="struct" hint="add/edit rate card size/placements">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.rateCardID = val(arguments.event.getValue('rateCardID',0))>
		<cfset local.rateCardConfigID = val(arguments.event.getValue('rateCardConfigID',0))>
		<cfset local.qryRateCardConfig = getRateCardConfig(rateCardConfigID=local.rateCardConfigID, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.saveRateCardConfigLink = buildCurrentLink(arguments.event,"saveRateCardConfig") & "&mode=stream">
		
		<!--- Setup GL Account Widget for Revenue GL --->
		<cfset local.strRevenueGLAcctWidgetData = {
			"label": "Revenue Account for New Events",
			"btnTxt": "Choose GL Account",
			"glatid": 3,
			"widgetMode": "GLSelector",
			"idFldName": "revenueGLAccountID",
			"idFldValue": val(local.qryRateCardConfig.revenueGLAccountID),
			"pathFldValue": len(local.qryRateCardConfig.GLAccountPath) ? local.qryRateCardConfig.GLAccountPath : "",
			"pathNoneTxt": "(no account selected)"
		}>
		<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_adInvoicing_rateCardConfig.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="saveRateCardConfig" access="public" output="false" returntype="struct" >
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.rateCardID = val(arguments.event.getValue('rateCardID',0))>
		<cfset local.rateCardConfigID = val(arguments.event.getValue('rateCardConfigID',0))>

		<cftry>
			<cfquery name="local.qrySaveRateCardConfig" datasource="#application.dsn.customApps.dsn#">
				<cfif local.rateCardConfigID gt 0>
					UPDATE dbo.ACBAR_AdInvoicing_rateCardConfigs
					SET configName = <cfqueryparam value="#arguments.event.getValue('configName','')#" cfsqltype="cf_sql_varchar" />,
						runs = <cfqueryparam value="#arguments.event.getValue('runs',0)#" cfsqltype="cf_sql_integer" />,
						pricePerRun = <cfqueryparam value="#arguments.event.getValue('pricePerRun',0)#" cfsqltype="cf_sql_decimal" />,
						allowRunOverride = <cfqueryparam value="#arguments.event.getValue('allowRunOverride',0)#" cfsqltype="cf_sql_bit" />,
						allowPriceOverride = <cfqueryparam value="#arguments.event.getValue('allowPriceOverride',0)#" cfsqltype="cf_sql_bit" />,
						revenueGLAccountID = <cfqueryparam value="#arguments.event.getValue('revenueGLAccountID',0)#" cfsqltype="cf_sql_integer" />
					WHERE rateCardConfigID = <cfqueryparam value="#local.rateCardConfigID#" cfsqltype="cf_sql_integer" />
				<cfelse>
					INSERT INTO dbo.ACBAR_AdInvoicing_rateCardConfigs(configName, rateCardID, runs,
						pricePerRun, allowRunOverride, allowPriceOverride, revenueGLAccountID, createdDate)
					VALUES(
						<cfqueryparam value="#arguments.event.getValue('configName','')#" cfsqltype="cf_sql_varchar" />,
						<cfqueryparam value="#local.rateCardID#" cfsqltype="cf_sql_integer" />,
						<cfqueryparam value="#arguments.event.getValue('runs',0)#" cfsqltype="cf_sql_integer" />,
						<cfqueryparam value="#arguments.event.getValue('pricePerRun',0)#" cfsqltype="cf_sql_decimal" />,
						<cfqueryparam value="#arguments.event.getValue('allowRunOverride',0)#" cfsqltype="cf_sql_bit" />,
						<cfqueryparam value="#arguments.event.getValue('allowPriceOverride',0)#" cfsqltype="cf_sql_bit" />,
						<cfqueryparam value="#arguments.event.getValue('revenueGLAccountID',0)#" cfsqltype="cf_sql_integer" />,
						GETDATE()
					)
				</cfif>
			</cfquery>
			
			<cfset local.success = true />
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.success = false />
		</cfcatch>
		</cftry>

		<cfset local.data = serializeJSON({ "success":local.success })>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRateCardConfig" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric"  required="true" />
		<cfargument name="rateCardConfigID" type="numeric"  required="true" />

		<cfset var local = structNew() />

		<cfquery name="local.qryRateCardConfig" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT c.rateCardConfigID, c.configName, c.rateCardID, c.runs, c.pricePerRun, c.allowRunOverride,
				c.allowPriceOverride, c.revenueGLAccountID, c.createdDate, rgl.thePathExpanded AS GLAccountPath
			FROM dbo.ACBAR_AdInvoicing_rateCardConfigs AS c
			INNER JOIN membercentral.dbo.tr_GLAccounts AS gl ON gl.GLAccountID = c.revenueGLAccountID
				AND gl.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			INNER JOIN membercentral.dbo.fn_getRecursiveGLAccounts(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">) AS rgl ON rgl.GLAccountID = gl.GLAccountID
			WHERE c.rateCardConfigID = <cfqueryparam value="#arguments.rateCardConfigID#" cfsqltype="cf_sql_integer" />
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryRateCardConfig />
	</cffunction>

	<cffunction  name="deleteRateCardConfig" access="public" output="false" returntype="struct">
		<cfargument name="rateCardConfigID" type="numeric"  required="true" />

		<cfset var local = structNew()>
		<cfset local.result = StructNew()>

		<cftry>
			<cfquery name="local.qryDelete" datasource="#application.dsn.customApps.dsn#">
				SET NOCOUNT ON;

				DECLARE @rateCardConfigID int = <cfqueryparam cfsqltype="cf_sql_integer"  value="#arguments.rateCardConfigID#" />;
				
				IF NOT EXISTS (SELECT 1 FROM dbo.ACBAR_AdInvoicing_insertionOrders WHERE rateCardConfigID = @rateCardConfigID)
					DELETE
					FROM dbo.ACBAR_AdInvoicing_rateCardConfigs
					WHERE rateCardConfigID = @rateCardConfigID;
			</cfquery>
			
			<cfset local.result.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.result.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.result>
	</cffunction>

</cfcomponent>