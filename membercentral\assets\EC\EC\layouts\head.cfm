<cfoutput>
    <head>
        <meta charset="utf-8">        
        <cfif len(event.getValue('mc_pageDefinition.pagekeywords',''))>
            <meta name="keywords" content="#event.getValue('mc_pageDefinition.pagekeywords','')#">
        </cfif>
        <cfif len(event.getValue('mc_pageDefinition.pageDescription',''))>
            <meta name="description" content="#event.getValue('mc_pageDefinition.pageDescription','')#">
        </cfif>	
        <title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
        
        #application.objCMS.getBootstrapHeadHTML()#
        #application.objCMS.getResponsiveHeadHTML()#
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
        <!--Css FIles Attachments-->        
        <link href="/css/main.css" rel="stylesheet" type="text/css">
        <link href="/assets/common/javascript/owlCarousel/233/owl.carousel.min.css" rel="stylesheet" type="text/css">
        <link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
        <link href="/css/responsive.css" rel="stylesheet" type="text/css">
        #application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
        <link href="//fonts.googleapis.com/css?family=Roboto:100,300,400,400i,500,700" rel="stylesheet">
        #application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
		<script>
			$(document).ready(function(){
				$("##frmLogin").attr('action', "/?pg=login&returnurl=#URLEncodedFormat("/?pg=mycela")#");
			});
		</script>
        <cfif application.MCEnvironment eq "production">
            <!-- Global site tag (gtag.js) - Google Analytics -->
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                
                gtag('config', 'G-N9J19KTTD6');
            </script>
        </cfif>
    </head>
</cfoutput>